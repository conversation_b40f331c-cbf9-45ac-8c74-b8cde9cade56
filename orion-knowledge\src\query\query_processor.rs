//! # 统一查询处理器
//!
//! 整合所有查询功能，提供简洁易用的统一查询接口。

use crate::{
    config::QueryConfig,
    error::Result,
    knowledge_base::SearchResult,
    query::{
        query_cache::{QueryCache, CacheConfig},
        query_parser::QueryParser,
        result_ranker::{ResultRanker, RankingConfig},
        search_optimizer::{SearchOptimizer, OptimizerConfig},
        ProcessedQuery,
    },
    storage::{
        hybrid_search::HybridSearchEngine,
        keyword_search::KeywordSearchEngine,
        VectorDatabase,
    },
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Instant;

/// 查询处理器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryProcessorConfig {
    /// 基础查询配置
    pub query_config: QueryConfig,
    /// 缓存配置
    pub cache_config: CacheConfig,
    /// 排序配置
    pub ranking_config: RankingConfig,
    /// 优化器配置
    pub optimizer_config: OptimizerConfig,
    /// 是否启用详细日志
    pub enable_detailed_logging: bool,
    /// 查询超时时间（毫秒）
    pub query_timeout_ms: u64,
}

impl Default for QueryProcessorConfig {
    fn default() -> Self {
        Self {
            query_config: QueryConfig::default(),
            cache_config: CacheConfig::default(),
            ranking_config: RankingConfig::default(),
            optimizer_config: OptimizerConfig::default(),
            enable_detailed_logging: false,
            query_timeout_ms: 30000, // 30秒
        }
    }
}

/// 查询响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryResponse {
    /// 搜索结果
    pub results: Vec<SearchResult>,
    /// 查询耗时（毫秒）
    pub duration_ms: u64,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 总结果数（分页前）
    pub total_count: usize,
    /// 查询统计信息
    pub stats: QueryStats,
}

/// 查询统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct QueryStats {
    /// 解析耗时（毫秒）
    pub parse_time_ms: u64,
    /// 优化耗时（毫秒）
    pub optimize_time_ms: u64,
    /// 搜索耗时（毫秒）
    pub search_time_ms: u64,
    /// 排序耗时（毫秒）
    pub rank_time_ms: u64,
    /// 向量搜索结果数
    pub vector_results_count: usize,
    /// 关键词搜索结果数
    pub keyword_results_count: usize,
    /// 最终结果数
    pub final_results_count: usize,
}

/// 统一查询处理器
pub struct QueryProcessor {
    /// 配置
    config: QueryProcessorConfig,
    /// 查询解析器
    parser: QueryParser,
    /// 搜索优化器
    optimizer: SearchOptimizer,
    /// 结果排序器
    ranker: ResultRanker,
    /// 查询缓存
    cache: QueryCache,
    /// 向量数据库
    vector_db: Arc<VectorDatabase>,
}

impl QueryProcessor {
    /// 创建新的查询处理器
    pub async fn new(
        config: QueryProcessorConfig,
        vector_db: Arc<VectorDatabase>,
    ) -> Result<Self> {
        let parser = QueryParser::new();
        let optimizer = SearchOptimizer::with_optimizer_config(
            &config.query_config,
            config.optimizer_config.clone(),
        );
        let ranker = ResultRanker::with_ranking_config(
            &config.query_config,
            config.ranking_config.clone(),
        );
        let cache = QueryCache::new(config.cache_config.clone());

        // 简化实现：直接使用 VectorDatabase，而不是 HybridSearchEngine
        Ok(Self {
            config,
            parser,
            optimizer,
            ranker,
            cache,
            vector_db,
        })
    }

    /// 执行查询
    pub async fn query(&self, query_text: &str) -> Result<QueryResponse> {
        let start_time = Instant::now();
        let mut stats = QueryStats::default();

        if self.config.enable_detailed_logging {
            tracing::info!("开始处理查询: {}", query_text);
        }

        // 1. 解析查询
        let parse_start = Instant::now();
        let mut processed_query = self.parser.parse(query_text)?;
        stats.parse_time_ms = parse_start.elapsed().as_millis() as u64;

        if self.config.enable_detailed_logging {
            tracing::debug!("查询解析完成: {:?}", processed_query);
        }

        // 2. 检查缓存
        if let Some(cached_results) = self.cache.get(&processed_query).await {
            let duration_ms = start_time.elapsed().as_millis() as u64;
            
            if self.config.enable_detailed_logging {
                tracing::info!("缓存命中，返回 {} 个结果", cached_results.len());
            }

            return Ok(QueryResponse {
                results: cached_results.clone(),
                duration_ms,
                from_cache: true,
                total_count: cached_results.len(),
                stats,
            });
        }

        // 3. 优化查询
        let optimize_start = Instant::now();
        processed_query = self.optimizer.optimize(processed_query).await?;
        stats.optimize_time_ms = optimize_start.elapsed().as_millis() as u64;

        if self.config.enable_detailed_logging {
            tracing::debug!("查询优化完成: {:?}", processed_query);
        }

        // 4. 执行搜索（简化实现：只使用关键词搜索）
        let search_start = Instant::now();
        let search_results = self.vector_db.search_keywords(&processed_query.keywords, processed_query.top_k).await?;
        stats.search_time_ms = search_start.elapsed().as_millis() as u64;
        stats.final_results_count = search_results.len();

        if self.config.enable_detailed_logging {
            tracing::debug!("搜索完成，找到 {} 个结果", search_results.len());
        }

        // 5. 结果排序和重排序
        let rank_start = Instant::now();
        let ranked_results = self.ranker.rerank(search_results, &processed_query).await?;
        stats.rank_time_ms = rank_start.elapsed().as_millis() as u64;

        if self.config.enable_detailed_logging {
            tracing::debug!("结果排序完成，最终 {} 个结果", ranked_results.len());
        }

        let total_duration_ms = start_time.elapsed().as_millis() as u64;

        // 6. 缓存结果（即使结果为空也缓存，避免重复查询）
        if let Err(e) = self.cache.put(&processed_query, ranked_results.clone(), total_duration_ms).await {
            tracing::warn!("缓存存储失败: {}", e);
        }

        // 7. 构建响应
        let response = QueryResponse {
            results: ranked_results.clone(),
            duration_ms: total_duration_ms,
            from_cache: false,
            total_count: ranked_results.len(),
            stats,
        };

        if self.config.enable_detailed_logging {
            tracing::info!(
                "查询处理完成，耗时 {}ms，返回 {} 个结果",
                total_duration_ms,
                ranked_results.len()
            );
        }

        Ok(response)
    }

    /// 批量查询
    pub async fn batch_query(&self, queries: Vec<&str>) -> Result<Vec<QueryResponse>> {
        let mut responses = Vec::with_capacity(queries.len());
        
        for query in queries {
            let response = self.query(query).await?;
            responses.push(response);
        }
        
        Ok(responses)
    }

    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> crate::query::query_cache::CacheStats {
        self.cache.get_stats()
    }

    /// 清空缓存
    pub async fn clear_cache(&self) -> Result<()> {
        self.cache.clear().await
    }

    /// 缓存预热
    pub async fn preload_cache(&self, common_queries: Vec<&str>) -> Result<()> {
        let mut processed_queries = Vec::new();
        
        for query_text in common_queries {
            if let Ok(processed_query) = self.parser.parse(query_text) {
                processed_queries.push(processed_query);
            }
        }
        
        self.cache.preload(processed_queries).await
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<HealthStatus> {
        let cache_stats = self.get_cache_stats();
        
        Ok(HealthStatus {
            is_healthy: true,
            cache_enabled: self.config.cache_config.enabled,
            cache_hit_rate: cache_stats.hit_rate,
            cache_entries: cache_stats.current_entries,
            uptime_seconds: 0, // 简化实现
        })
    }

    /// 获取查询建议
    pub async fn get_suggestions(&self, partial_query: &str) -> Result<Vec<String>> {
        // 简化实现：基于常见查询模式生成建议
        let suggestions = vec![
            format!("{} function", partial_query),
            format!("{} class", partial_query),
            format!("{} method", partial_query),
            format!("how to {}", partial_query),
            format!("{} example", partial_query),
        ];
        
        Ok(suggestions.into_iter().take(5).collect())
    }
}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    /// 是否健康
    pub is_healthy: bool,
    /// 缓存是否启用
    pub cache_enabled: bool,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 缓存条目数
    pub cache_entries: usize,
    /// 运行时间（秒）
    pub uptime_seconds: u64,
}

/// 查询处理器构建器
pub struct QueryProcessorBuilder {
    config: QueryProcessorConfig,
}

impl QueryProcessorBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: QueryProcessorConfig::default(),
        }
    }

    /// 设置查询配置
    pub fn with_query_config(mut self, config: QueryConfig) -> Self {
        self.config.query_config = config;
        self
    }

    /// 设置缓存配置
    pub fn with_cache_config(mut self, config: CacheConfig) -> Self {
        self.config.cache_config = config;
        self
    }

    /// 设置排序配置
    pub fn with_ranking_config(mut self, config: RankingConfig) -> Self {
        self.config.ranking_config = config;
        self
    }

    /// 设置优化器配置
    pub fn with_optimizer_config(mut self, config: OptimizerConfig) -> Self {
        self.config.optimizer_config = config;
        self
    }

    /// 启用详细日志
    pub fn enable_detailed_logging(mut self) -> Self {
        self.config.enable_detailed_logging = true;
        self
    }

    /// 设置查询超时时间
    pub fn with_timeout(mut self, timeout_ms: u64) -> Self {
        self.config.query_timeout_ms = timeout_ms;
        self
    }

    /// 构建查询处理器
    pub async fn build(
        self,
        vector_db: Arc<VectorDatabase>,
    ) -> Result<QueryProcessor> {
        QueryProcessor::new(self.config, vector_db).await
    }
}

impl Default for QueryProcessorBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        config::DatabaseConfig,
        storage::VectorDatabase,
    };
    use tempfile::NamedTempFile;

    async fn create_test_vector_db() -> Arc<VectorDatabase> {
        let temp_db = NamedTempFile::new().unwrap();
        let config = DatabaseConfig {
            path: temp_db.path().to_path_buf(),
            pool_size: 1,
            query_timeout: 30,
            enable_wal: false,
            cache_size_mb: 16,
        };

        Arc::new(VectorDatabase::new(&config).await.unwrap())
    }

    #[tokio::test]
    async fn test_query_processor_creation() {
        let vector_db = create_test_vector_db().await;
        let config = QueryProcessorConfig::default();

        let processor = QueryProcessor::new(config, vector_db).await;
        assert!(processor.is_ok());
    }

    #[tokio::test]
    async fn test_query_execution() {
        let vector_db = create_test_vector_db().await;
        let config = QueryProcessorConfig::default();

        let processor = QueryProcessor::new(config, vector_db).await.unwrap();

        // 执行简单查询
        let response = processor.query("test function").await;
        assert!(response.is_ok());

        let response = response.unwrap();
        assert!(!response.from_cache);
        assert!(response.duration_ms > 0);
    }

    #[tokio::test]
    async fn test_cache_functionality() {
        let vector_db = create_test_vector_db().await;
        let mut config = QueryProcessorConfig::default();
        config.cache_config.enabled = true;
        // 禁用查询优化以确保缓存键一致
        config.optimizer_config.enable_query_rewrite = false;
        config.optimizer_config.enable_stop_word_filter = false;
        config.optimizer_config.enable_synonym_expansion = false;
        config.optimizer_config.enable_stemming = false;

        let processor = QueryProcessor::new(config, vector_db).await.unwrap();

        // 第一次查询
        let response1 = processor.query("test function").await.unwrap();
        assert!(!response1.from_cache);

        // 第二次查询应该来自缓存
        let response2 = processor.query("test function").await.unwrap();
        assert!(response2.from_cache);
    }

    #[tokio::test]
    async fn test_batch_query() {
        let vector_db = create_test_vector_db().await;
        let config = QueryProcessorConfig::default();

        let processor = QueryProcessor::new(config, vector_db).await.unwrap();

        let queries = vec!["test function", "hello world", "rust code"];
        let responses = processor.batch_query(queries).await.unwrap();

        assert_eq!(responses.len(), 3);
        for response in responses {
            assert!(response.duration_ms > 0);
        }
    }

    #[tokio::test]
    async fn test_health_check() {
        let vector_db = create_test_vector_db().await;
        let config = QueryProcessorConfig::default();

        let processor = QueryProcessor::new(config, vector_db).await.unwrap();

        let health = processor.health_check().await.unwrap();
        assert!(health.is_healthy);
    }

    #[tokio::test]
    async fn test_query_suggestions() {
        let vector_db = create_test_vector_db().await;
        let config = QueryProcessorConfig::default();

        let processor = QueryProcessor::new(config, vector_db).await.unwrap();

        let suggestions = processor.get_suggestions("test").await.unwrap();
        assert!(!suggestions.is_empty());
        assert!(suggestions.iter().any(|s| s.contains("test")));
    }

    #[tokio::test]
    async fn test_builder_pattern() {
        let vector_db = create_test_vector_db().await;

        let processor = QueryProcessorBuilder::new()
            .enable_detailed_logging()
            .with_timeout(5000)
            .build(vector_db)
            .await;

        assert!(processor.is_ok());
        let processor = processor.unwrap();
        assert!(processor.config.enable_detailed_logging);
        assert_eq!(processor.config.query_timeout_ms, 5000);
    }
}
