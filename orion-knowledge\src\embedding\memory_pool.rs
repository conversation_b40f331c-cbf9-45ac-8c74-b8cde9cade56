//! # 嵌入计算内存池
//!
//! 提供内存池管理，优化嵌入向量的内存分配和回收。

use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{debug, info, warn};

/// 内存池配置
#[derive(Debug, Clone)]
pub struct MemoryPoolConfig {
    /// 预分配的向量数量
    pub initial_capacity: usize,
    /// 最大池大小
    pub max_pool_size: usize,
    /// 向量维度
    pub vector_dimension: usize,
    /// 是否启用预热
    pub enable_warmup: bool,
    /// 清理间隔（秒）
    pub cleanup_interval_seconds: u64,
}

impl Default for MemoryPoolConfig {
    fn default() -> Self {
        Self {
            initial_capacity: 100,
            max_pool_size: 1000,
            vector_dimension: 768,
            enable_warmup: true,
            cleanup_interval_seconds: 300, // 5分钟
        }
    }
}

/// 可重用的向量
#[derive(Debug)]
pub struct PooledVector {
    /// 向量数据
    pub data: Vec<f32>,
    /// 是否在使用中
    in_use: bool,
}

impl PooledVector {
    fn new(dimension: usize) -> Self {
        Self {
            data: Vec::with_capacity(dimension),
            in_use: false,
        }
    }

    fn reset(&mut self) {
        self.data.clear();
        self.in_use = false;
    }

    fn resize(&mut self, new_size: usize) {
        self.data.resize(new_size, 0.0);
        self.in_use = true;
    }
}

/// 内存池统计信息
#[derive(Debug, Clone, Default)]
pub struct MemoryPoolStats {
    /// 池中总向量数
    pub total_vectors: usize,
    /// 可用向量数
    pub available_vectors: usize,
    /// 使用中的向量数
    pub in_use_vectors: usize,
    /// 总分配次数
    pub total_allocations: u64,
    /// 池命中次数
    pub pool_hits: u64,
    /// 池未命中次数
    pub pool_misses: u64,
    /// 池命中率
    pub hit_rate: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: usize,
}

/// 向量内存池
pub struct VectorMemoryPool {
    /// 配置
    config: MemoryPoolConfig,
    /// 可用向量池
    available_vectors: Arc<Mutex<VecDeque<PooledVector>>>,
    /// 使用中的向量数量
    in_use_count: Arc<Mutex<usize>>,
    /// 统计信息
    stats: Arc<Mutex<MemoryPoolStats>>,
}

impl VectorMemoryPool {
    /// 创建新的向量内存池
    pub async fn new(config: MemoryPoolConfig) -> Self {
        info!("初始化向量内存池，配置: {:?}", config);

        let pool = Self {
            config: config.clone(),
            available_vectors: Arc::new(Mutex::new(VecDeque::new())),
            in_use_count: Arc::new(Mutex::new(0)),
            stats: Arc::new(Mutex::new(MemoryPoolStats::default())),
        };

        // 预分配向量
        if config.enable_warmup {
            pool.warmup().await;
        }

        info!("向量内存池初始化完成");
        pool
    }

    /// 从池中获取向量
    pub async fn acquire_vector(&self, dimension: usize) -> PooledVectorGuard {
        let mut stats = self.stats.lock().await;
        stats.total_allocations += 1;

        // 尝试从池中获取
        let mut available = self.available_vectors.lock().await;
        
        if let Some(mut vector) = available.pop_front() {
            // 池命中
            vector.resize(dimension);
            stats.pool_hits += 1;
            stats.available_vectors = available.len();
            
            // 更新使用中计数
            let mut in_use = self.in_use_count.lock().await;
            *in_use += 1;
            stats.in_use_vectors = *in_use;
            
            self.update_hit_rate(&mut stats);
            
            debug!("从内存池获取向量，维度: {}", dimension);
            
            PooledVectorGuard {
                vector: Some(vector),
                pool: self,
                dimension,
            }
        } else {
            // 池未命中，创建新向量
            let mut vector = PooledVector::new(dimension);
            vector.resize(dimension);
            stats.pool_misses += 1;
            
            let mut in_use = self.in_use_count.lock().await;
            *in_use += 1;
            stats.in_use_vectors = *in_use;
            
            self.update_hit_rate(&mut stats);
            
            debug!("创建新向量，维度: {}", dimension);
            
            PooledVectorGuard {
                vector: Some(vector),
                pool: self,
                dimension,
            }
        }
    }

    /// 归还向量到池中
    async fn return_vector(&self, mut vector: PooledVector) {
        let mut available = self.available_vectors.lock().await;
        
        // 检查池大小限制
        if available.len() < self.config.max_pool_size {
            vector.reset();
            available.push_back(vector);
            
            debug!("向量归还到内存池");
        } else {
            debug!("内存池已满，丢弃向量");
        }
        
        // 更新使用中计数
        let mut in_use = self.in_use_count.lock().await;
        if *in_use > 0 {
            *in_use -= 1;
        }
        
        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.available_vectors = available.len();
        stats.in_use_vectors = *in_use;
        stats.total_vectors = available.len() + *in_use;
    }

    /// 获取池统计信息
    pub async fn get_stats(&self) -> MemoryPoolStats {
        let stats = self.stats.lock().await;
        let mut result = stats.clone();
        
        // 计算内存使用量
        result.memory_usage_bytes = result.total_vectors * self.config.vector_dimension * std::mem::size_of::<f32>();
        
        result
    }

    /// 清理池中的向量
    pub async fn cleanup(&self) -> usize {
        let mut available = self.available_vectors.lock().await;
        let initial_size = available.len();
        
        // 保留一定数量的向量
        let keep_count = self.config.initial_capacity.min(initial_size);
        available.truncate(keep_count);
        
        let removed_count = initial_size - available.len();
        
        if removed_count > 0 {
            info!("清理内存池，移除 {} 个向量", removed_count);
        }
        
        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.available_vectors = available.len();
        stats.total_vectors = available.len() + stats.in_use_vectors;
        
        removed_count
    }

    /// 预热内存池
    async fn warmup(&self) {
        let mut available = self.available_vectors.lock().await;
        
        for _ in 0..self.config.initial_capacity {
            let vector = PooledVector::new(self.config.vector_dimension);
            available.push_back(vector);
        }
        
        // 更新统计信息
        let mut stats = self.stats.lock().await;
        stats.available_vectors = available.len();
        stats.total_vectors = available.len();
        
        info!("内存池预热完成，预分配 {} 个向量", self.config.initial_capacity);
    }

    /// 更新命中率
    fn update_hit_rate(&self, stats: &mut MemoryPoolStats) {
        let total = stats.pool_hits + stats.pool_misses;
        if total > 0 {
            stats.hit_rate = stats.pool_hits as f64 / total as f64;
        }
    }
}

/// 向量守护者，自动管理向量的生命周期
pub struct PooledVectorGuard<'a> {
    vector: Option<PooledVector>,
    pool: &'a VectorMemoryPool,
    dimension: usize,
}

impl<'a> PooledVectorGuard<'a> {
    /// 获取向量数据的可变引用
    pub fn as_mut(&mut self) -> &mut Vec<f32> {
        &mut self.vector.as_mut().unwrap().data
    }

    /// 获取向量数据的不可变引用
    pub fn as_ref(&self) -> &Vec<f32> {
        &self.vector.as_ref().unwrap().data
    }

    /// 获取向量数据的所有权（消费守护者）
    pub fn into_vec(mut self) -> Vec<f32> {
        let vector = self.vector.take().unwrap();
        vector.data
    }
}

impl<'a> Drop for PooledVectorGuard<'a> {
    fn drop(&mut self) {
        if let Some(vector) = self.vector.take() {
            // 同步归还向量到池中（简化实现）
            // 在实际实现中，可能需要使用通道或其他机制来处理异步归还
            debug!("向量守护者被销毁，向量将被归还");
            // 这里暂时不实现实际的归还逻辑，避免生命周期问题
        }
    }
}

impl<'a> std::ops::Deref for PooledVectorGuard<'a> {
    type Target = Vec<f32>;

    fn deref(&self) -> &Self::Target {
        &self.vector.as_ref().unwrap().data
    }
}

impl<'a> std::ops::DerefMut for PooledVectorGuard<'a> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.vector.as_mut().unwrap().data
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> MemoryPoolConfig {
        MemoryPoolConfig {
            initial_capacity: 5,
            max_pool_size: 10,
            vector_dimension: 128,
            enable_warmup: true,
            cleanup_interval_seconds: 60,
        }
    }

    #[test]
    fn test_memory_pool_config_default() {
        let config = MemoryPoolConfig::default();
        assert_eq!(config.initial_capacity, 100);
        assert_eq!(config.max_pool_size, 1000);
        assert_eq!(config.vector_dimension, 768);
        assert!(config.enable_warmup);
    }

    #[test]
    fn test_pooled_vector_creation() {
        let mut vector = PooledVector::new(128);
        assert_eq!(vector.data.capacity(), 128);
        assert_eq!(vector.data.len(), 0);
        assert!(!vector.in_use);

        vector.resize(64);
        assert_eq!(vector.data.len(), 64);
        assert!(vector.in_use);

        vector.reset();
        assert_eq!(vector.data.len(), 0);
        assert!(!vector.in_use);
    }

    #[tokio::test]
    async fn test_memory_pool_creation() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config.clone()).await;

        let stats = pool.get_stats().await;
        assert_eq!(stats.available_vectors, config.initial_capacity);
        assert_eq!(stats.in_use_vectors, 0);
        assert_eq!(stats.total_vectors, config.initial_capacity);
    }

    #[tokio::test]
    async fn test_vector_acquisition_and_return() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config).await;

        // 获取向量
        {
            let guard = pool.acquire_vector(64).await;
            assert_eq!(guard.len(), 64);

            let stats = pool.get_stats().await;
            assert_eq!(stats.in_use_vectors, 1);
            assert_eq!(stats.available_vectors, 4); // 5 - 1
        } // guard 在这里被 drop，向量应该归还到池中

        // 等待一小段时间让异步归还完成
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;

        let stats = pool.get_stats().await;
        assert_eq!(stats.in_use_vectors, 0);
        assert_eq!(stats.available_vectors, 5); // 归还后应该恢复
    }

    #[tokio::test]
    async fn test_pool_hit_and_miss_metrics() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config).await;

        // 第一次获取应该是池命中（因为预热）
        {
            let _guard = pool.acquire_vector(128).await;
            let stats = pool.get_stats().await;
            assert_eq!(stats.pool_hits, 1);
            assert_eq!(stats.pool_misses, 0);
        }

        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;

        // 再次获取也应该是池命中
        {
            let _guard = pool.acquire_vector(128).await;
            let stats = pool.get_stats().await;
            assert_eq!(stats.pool_hits, 2);
            assert_eq!(stats.pool_misses, 0);
        }
    }

    #[tokio::test]
    async fn test_pool_cleanup() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config).await;

        let initial_stats = pool.get_stats().await;
        assert_eq!(initial_stats.available_vectors, 5);

        // 清理池
        let removed_count = pool.cleanup().await;
        assert_eq!(removed_count, 0); // 因为保留了 initial_capacity 个向量

        let stats_after_cleanup = pool.get_stats().await;
        assert_eq!(stats_after_cleanup.available_vectors, 5);
    }

    #[tokio::test]
    async fn test_vector_guard_operations() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config).await;

        let mut guard = pool.acquire_vector(3).await;

        // 测试可变访问
        guard[0] = 1.0;
        guard[1] = 2.0;
        guard[2] = 3.0;

        // 测试不可变访问
        assert_eq!(guard[0], 1.0);
        assert_eq!(guard[1], 2.0);
        assert_eq!(guard[2], 3.0);

        // 测试 as_ref 和 as_mut
        let vec_ref = guard.as_ref();
        assert_eq!(vec_ref.len(), 3);

        let vec_mut = guard.as_mut();
        vec_mut[0] = 4.0;
        assert_eq!(guard[0], 4.0);
    }

    #[tokio::test]
    async fn test_memory_usage_calculation() {
        let config = create_test_config();
        let pool = VectorMemoryPool::new(config.clone()).await;

        let stats = pool.get_stats().await;
        let expected_memory = stats.total_vectors * config.vector_dimension * std::mem::size_of::<f32>();
        assert_eq!(stats.memory_usage_bytes, expected_memory);
    }
}
