//! # 嵌入模型注册表
//!
//! 管理所有可用的嵌入模型，包括模型发现、注册、配置和元数据管理。

use crate::{
    config::EmbeddingConfig,
    error::Result,
    embedding::{
        performance::PerformanceConfig,
        manager::EmbedderType,
    },
    KnowledgeError,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use rand::Rng;

/// 模型状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModelStatus {
    /// 未初始化
    Uninitialized,
    /// 初始化中
    Initializing,
    /// 可用
    Available,
    /// 不可用
    Unavailable,
    /// 错误状态
    Error(String),
    /// 维护中
    Maintenance,
}

impl std::fmt::Display for ModelStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ModelStatus::Uninitialized => write!(f, "未初始化"),
            ModelStatus::Initializing => write!(f, "初始化中"),
            ModelStatus::Available => write!(f, "可用"),
            ModelStatus::Unavailable => write!(f, "不可用"),
            ModelStatus::Error(msg) => write!(f, "错误: {}", msg),
            ModelStatus::Maintenance => write!(f, "维护中"),
        }
    }
}

/// 模型性能指标
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ModelMetrics {
    /// 总请求数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: f64,
    /// 吞吐量（请求/秒）
    pub throughput_rps: f64,
    /// 错误率
    pub error_rate: f64,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

impl ModelMetrics {
    /// 更新指标
    pub fn update(&mut self, response_time_ms: f64, success: bool) {
        self.total_requests += 1;
        
        if success {
            self.successful_requests += 1;
        } else {
            self.failed_requests += 1;
        }
        
        // 更新响应时间统计
        if self.total_requests == 1 {
            self.avg_response_time_ms = response_time_ms;
            self.min_response_time_ms = response_time_ms;
            self.max_response_time_ms = response_time_ms;
        } else {
            self.avg_response_time_ms = (self.avg_response_time_ms * (self.total_requests - 1) as f64 + response_time_ms) / self.total_requests as f64;
            self.min_response_time_ms = self.min_response_time_ms.min(response_time_ms);
            self.max_response_time_ms = self.max_response_time_ms.max(response_time_ms);
        }
        
        // 更新错误率
        self.error_rate = self.failed_requests as f64 / self.total_requests as f64;
        
        // 更新时间戳
        self.last_updated = chrono::Utc::now();
    }
    
    /// 计算吞吐量
    pub fn calculate_throughput(&mut self, time_window_seconds: f64) {
        if time_window_seconds > 0.0 {
            self.throughput_rps = self.total_requests as f64 / time_window_seconds;
        }
    }
}

/// 模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// 模型 ID
    pub model_id: String,
    /// 模型类型
    pub model_type: EmbedderType,
    /// 模型名称
    pub model_name: String,
    /// 模型描述
    pub description: String,
    /// 向量维度
    pub dimension: usize,
    /// 最大输入长度
    pub max_input_length: usize,
    /// 是否支持批量处理
    pub supports_batch: bool,
    /// 优先级（数字越小优先级越高）
    pub priority: u32,
    /// 权重（用于负载均衡）
    pub weight: f32,
    /// 嵌入配置
    pub embedding_config: EmbeddingConfig,
    /// 性能配置
    pub performance_config: PerformanceConfig,
    /// 自定义标签
    pub tags: HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl ModelConfig {
    /// 创建新的模型配置
    pub fn new(
        model_id: String,
        model_type: EmbedderType,
        model_name: String,
        dimension: usize,
    ) -> Self {
        let now = chrono::Utc::now();
        Self {
            model_id,
            model_type,
            model_name,
            description: String::new(),
            dimension,
            max_input_length: 8192,
            supports_batch: true,
            priority: 100,
            weight: 1.0,
            embedding_config: EmbeddingConfig::default(),
            performance_config: PerformanceConfig::default(),
            tags: HashMap::new(),
            created_at: now,
            updated_at: now,
        }
    }
    
    /// 添加标签
    pub fn with_tag(mut self, key: String, value: String) -> Self {
        self.tags.insert(key, value);
        self.updated_at = chrono::Utc::now();
        self
    }
    
    /// 设置优先级
    pub fn with_priority(mut self, priority: u32) -> Self {
        self.priority = priority;
        self.updated_at = chrono::Utc::now();
        self
    }
    
    /// 设置权重
    pub fn with_weight(mut self, weight: f32) -> Self {
        self.weight = weight;
        self.updated_at = chrono::Utc::now();
        self
    }
}

/// 注册的模型信息
#[derive(Debug, Clone)]
pub struct RegisteredModel {
    /// 模型配置
    pub config: ModelConfig,
    /// 模型状态
    pub status: ModelStatus,
    /// 性能指标
    pub metrics: ModelMetrics,
    /// 最后健康检查时间
    pub last_health_check: Option<Instant>,
    /// 健康检查间隔
    pub health_check_interval: Duration,
}

impl RegisteredModel {
    /// 创建新的注册模型
    pub fn new(config: ModelConfig) -> Self {
        Self {
            config,
            status: ModelStatus::Uninitialized,
            metrics: ModelMetrics::default(),
            last_health_check: None,
            health_check_interval: Duration::from_secs(300), // 5分钟
        }
    }
    
    /// 检查是否需要健康检查
    pub fn needs_health_check(&self) -> bool {
        match self.last_health_check {
            Some(last_check) => last_check.elapsed() >= self.health_check_interval,
            None => true,
        }
    }
    
    /// 更新健康检查时间
    pub fn update_health_check(&mut self) {
        self.last_health_check = Some(Instant::now());
    }
    
    /// 更新状态
    pub fn update_status(&mut self, status: ModelStatus) {
        self.status = status;
        debug!("模型 {} 状态更新为: {}", self.config.model_id, self.status);
    }
    
    /// 更新性能指标
    pub fn update_metrics(&mut self, response_time_ms: f64, success: bool) {
        self.metrics.update(response_time_ms, success);
    }
}

/// 模型注册表
pub struct ModelRegistry {
    /// 注册的模型
    models: Arc<RwLock<HashMap<String, RegisteredModel>>>,
    /// 默认模型 ID
    default_model_id: Arc<RwLock<Option<String>>>,
}

impl ModelRegistry {
    /// 创建新的模型注册表
    pub fn new() -> Self {
        info!("初始化模型注册表");
        Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            default_model_id: Arc::new(RwLock::new(None)),
        }
    }
    
    /// 注册模型
    pub async fn register_model(&self, config: ModelConfig) -> Result<()> {
        let model_id = config.model_id.clone();
        let registered_model = RegisteredModel::new(config);
        
        let mut models = self.models.write().await;
        models.insert(model_id.clone(), registered_model);
        
        info!("模型注册成功: {}", model_id);
        Ok(())
    }
    
    /// 注销模型
    pub async fn unregister_model(&self, model_id: &str) -> Result<()> {
        let mut models = self.models.write().await;
        
        if models.remove(model_id).is_some() {
            info!("模型注销成功: {}", model_id);
            
            // 如果注销的是默认模型，清除默认模型设置
            let mut default_id = self.default_model_id.write().await;
            if default_id.as_ref() == Some(&model_id.to_string()) {
                *default_id = None;
                warn!("默认模型已注销，需要重新设置默认模型");
            }
            
            Ok(())
        } else {
            Err(KnowledgeError::embedding_error(&format!("模型不存在: {}", model_id)))
        }
    }
    
    /// 获取模型配置
    pub async fn get_model_config(&self, model_id: &str) -> Option<ModelConfig> {
        let models = self.models.read().await;
        models.get(model_id).map(|model| model.config.clone())
    }
    
    /// 获取模型状态
    pub async fn get_model_status(&self, model_id: &str) -> Option<ModelStatus> {
        let models = self.models.read().await;
        models.get(model_id).map(|model| model.status.clone())
    }
    
    /// 获取模型指标
    pub async fn get_model_metrics(&self, model_id: &str) -> Option<ModelMetrics> {
        let models = self.models.read().await;
        models.get(model_id).map(|model| model.metrics.clone())
    }
    
    /// 列出所有模型
    pub async fn list_models(&self) -> Vec<String> {
        let models = self.models.read().await;
        models.keys().cloned().collect()
    }
    
    /// 按状态筛选模型
    pub async fn list_models_by_status(&self, status: ModelStatus) -> Vec<String> {
        let models = self.models.read().await;
        models
            .iter()
            .filter(|(_, model)| model.status == status)
            .map(|(id, _)| id.clone())
            .collect()
    }
    
    /// 按标签筛选模型
    pub async fn list_models_by_tag(&self, tag_key: &str, tag_value: &str) -> Vec<String> {
        let models = self.models.read().await;
        models
            .iter()
            .filter(|(_, model)| {
                model.config.tags.get(tag_key) == Some(&tag_value.to_string())
            })
            .map(|(id, _)| id.clone())
            .collect()
    }

    /// 设置默认模型
    pub async fn set_default_model(&self, model_id: &str) -> Result<()> {
        let models = self.models.read().await;

        if models.contains_key(model_id) {
            let mut default_id = self.default_model_id.write().await;
            *default_id = Some(model_id.to_string());
            info!("设置默认模型: {}", model_id);
            Ok(())
        } else {
            Err(KnowledgeError::embedding_error(&format!("模型不存在: {}", model_id)))
        }
    }

    /// 获取默认模型 ID
    pub async fn get_default_model_id(&self) -> Option<String> {
        let default_id = self.default_model_id.read().await;
        default_id.clone()
    }

    /// 更新模型状态
    pub async fn update_model_status(&self, model_id: &str, status: ModelStatus) -> Result<()> {
        let mut models = self.models.write().await;

        if let Some(model) = models.get_mut(model_id) {
            model.update_status(status);
            Ok(())
        } else {
            Err(KnowledgeError::embedding_error(&format!("模型不存在: {}", model_id)))
        }
    }

    /// 更新模型指标
    pub async fn update_model_metrics(&self, model_id: &str, response_time_ms: f64, success: bool) -> Result<()> {
        let mut models = self.models.write().await;

        if let Some(model) = models.get_mut(model_id) {
            model.update_metrics(response_time_ms, success);
            Ok(())
        } else {
            Err(KnowledgeError::embedding_error(&format!("模型不存在: {}", model_id)))
        }
    }

    /// 获取需要健康检查的模型
    pub async fn get_models_needing_health_check(&self) -> Vec<String> {
        let models = self.models.read().await;
        models
            .iter()
            .filter(|(_, model)| model.needs_health_check())
            .map(|(id, _)| id.clone())
            .collect()
    }

    /// 更新模型健康检查时间
    pub async fn update_health_check(&self, model_id: &str) -> Result<()> {
        let mut models = self.models.write().await;

        if let Some(model) = models.get_mut(model_id) {
            model.update_health_check();
            Ok(())
        } else {
            Err(KnowledgeError::embedding_error(&format!("模型不存在: {}", model_id)))
        }
    }

    /// 获取最佳模型（基于性能和可用性）
    pub async fn get_best_model(&self, criteria: &ModelSelectionCriteria) -> Option<String> {
        let models = self.models.read().await;

        let mut candidates: Vec<_> = models
            .iter()
            .filter(|(_, model)| {
                // 只考虑可用的模型
                model.status == ModelStatus::Available &&
                // 检查维度匹配
                (criteria.dimension.is_none() || model.config.dimension == criteria.dimension.unwrap()) &&
                // 检查类型匹配
                (criteria.model_type.is_none() || model.config.model_type == *criteria.model_type.as_ref().unwrap()) &&
                // 检查标签匹配
                criteria.required_tags.iter().all(|(key, value)| {
                    model.config.tags.get(key) == Some(value)
                })
            })
            .collect();

        if candidates.is_empty() {
            return None;
        }

        // 根据选择策略排序
        match criteria.selection_strategy {
            ModelSelectionStrategy::LowestLatency => {
                candidates.sort_by(|a, b| {
                    a.1.metrics.avg_response_time_ms.partial_cmp(&b.1.metrics.avg_response_time_ms)
                        .unwrap_or(std::cmp::Ordering::Equal)
                });
            }
            ModelSelectionStrategy::HighestThroughput => {
                candidates.sort_by(|a, b| {
                    b.1.metrics.throughput_rps.partial_cmp(&a.1.metrics.throughput_rps)
                        .unwrap_or(std::cmp::Ordering::Equal)
                });
            }
            ModelSelectionStrategy::LowestErrorRate => {
                candidates.sort_by(|a, b| {
                    a.1.metrics.error_rate.partial_cmp(&b.1.metrics.error_rate)
                        .unwrap_or(std::cmp::Ordering::Equal)
                });
            }
            ModelSelectionStrategy::Priority => {
                candidates.sort_by_key(|a| a.1.config.priority);
            }
            ModelSelectionStrategy::WeightedRandom => {
                // 基于权重的随机选择
                let total_weight: f32 = candidates.iter().map(|(_, model)| model.config.weight).sum();
                if total_weight > 0.0 {
                    let mut rng = rand::thread_rng();
                    let random_value: f32 = rng.gen_range(0.0..total_weight);
                    let mut current_weight = 0.0;

                    for (id, model) in &candidates {
                        current_weight += model.config.weight;
                        if current_weight >= random_value {
                            return Some((*id).clone());
                        }
                    }
                }
            }
        }

        candidates.first().map(|(id, _)| (*id).clone())
    }

    /// 获取模型统计摘要
    pub async fn get_registry_summary(&self) -> RegistrySummary {
        let models = self.models.read().await;

        let mut summary = RegistrySummary::default();
        summary.total_models = models.len();

        for (_, model) in models.iter() {
            match model.status {
                ModelStatus::Available => summary.available_models += 1,
                ModelStatus::Unavailable => summary.unavailable_models += 1,
                ModelStatus::Error(_) => summary.error_models += 1,
                ModelStatus::Initializing => summary.initializing_models += 1,
                ModelStatus::Maintenance => summary.maintenance_models += 1,
                ModelStatus::Uninitialized => summary.uninitialized_models += 1,
            }

            // 统计模型类型
            let type_name = model.config.model_type.to_string();
            *summary.models_by_type.entry(type_name).or_insert(0) += 1;
        }

        summary
    }
}

/// 模型选择策略
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModelSelectionStrategy {
    /// 最低延迟
    LowestLatency,
    /// 最高吞吐量
    HighestThroughput,
    /// 最低错误率
    LowestErrorRate,
    /// 优先级
    Priority,
    /// 加权随机
    WeightedRandom,
}

/// 模型选择条件
#[derive(Debug, Clone, Default)]
pub struct ModelSelectionCriteria {
    /// 要求的维度
    pub dimension: Option<usize>,
    /// 要求的模型类型
    pub model_type: Option<EmbedderType>,
    /// 必需的标签
    pub required_tags: HashMap<String, String>,
    /// 选择策略
    pub selection_strategy: ModelSelectionStrategy,
}

impl Default for ModelSelectionStrategy {
    fn default() -> Self {
        ModelSelectionStrategy::Priority
    }
}

/// 注册表统计摘要
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct RegistrySummary {
    /// 总模型数
    pub total_models: usize,
    /// 可用模型数
    pub available_models: usize,
    /// 不可用模型数
    pub unavailable_models: usize,
    /// 错误模型数
    pub error_models: usize,
    /// 初始化中模型数
    pub initializing_models: usize,
    /// 维护中模型数
    pub maintenance_models: usize,
    /// 未初始化模型数
    pub uninitialized_models: usize,
    /// 按类型统计的模型数
    pub models_by_type: HashMap<String, usize>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    fn create_test_model_config(id: &str, model_type: EmbedderType) -> ModelConfig {
        ModelConfig::new(
            id.to_string(),
            model_type,
            format!("test-model-{}", id),
            768,
        )
        .with_priority(10)
        .with_weight(1.0)
        .with_tag("test".to_string(), "true".to_string())
    }

    #[tokio::test]
    async fn test_model_registry_creation() {
        let registry = ModelRegistry::new();
        let models = registry.list_models().await;
        assert!(models.is_empty());

        let default_model = registry.get_default_model_id().await;
        assert!(default_model.is_none());
    }

    #[tokio::test]
    async fn test_model_registration() {
        let registry = ModelRegistry::new();
        let config = create_test_model_config("test1", EmbedderType::Local);

        // 注册模型
        let result = registry.register_model(config.clone()).await;
        assert!(result.is_ok());

        // 验证模型已注册
        let models = registry.list_models().await;
        assert_eq!(models.len(), 1);
        assert!(models.contains(&"test1".to_string()));

        // 获取模型配置
        let retrieved_config = registry.get_model_config("test1").await;
        assert!(retrieved_config.is_some());
        let retrieved_config = retrieved_config.unwrap();
        assert_eq!(retrieved_config.model_id, "test1");
        assert_eq!(retrieved_config.model_type, EmbedderType::Local);
    }

    #[tokio::test]
    async fn test_model_unregistration() {
        let registry = ModelRegistry::new();
        let config = create_test_model_config("test1", EmbedderType::Local);

        // 注册模型
        registry.register_model(config).await.unwrap();
        assert_eq!(registry.list_models().await.len(), 1);

        // 注销模型
        let result = registry.unregister_model("test1").await;
        assert!(result.is_ok());

        // 验证模型已注销
        let models = registry.list_models().await;
        assert!(models.is_empty());

        // 尝试注销不存在的模型
        let result = registry.unregister_model("nonexistent").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_default_model_management() {
        let registry = ModelRegistry::new();
        let config = create_test_model_config("test1", EmbedderType::Local);

        registry.register_model(config).await.unwrap();

        // 设置默认模型
        let result = registry.set_default_model("test1").await;
        assert!(result.is_ok());

        let default_model = registry.get_default_model_id().await;
        assert_eq!(default_model, Some("test1".to_string()));

        // 尝试设置不存在的模型为默认
        let result = registry.set_default_model("nonexistent").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_model_status_management() {
        let registry = ModelRegistry::new();
        let config = create_test_model_config("test1", EmbedderType::Local);

        registry.register_model(config).await.unwrap();

        // 更新模型状态
        let result = registry.update_model_status("test1", ModelStatus::Available).await;
        assert!(result.is_ok());

        let status = registry.get_model_status("test1").await;
        assert_eq!(status, Some(ModelStatus::Available));

        // 更新为错误状态
        let result = registry.update_model_status("test1", ModelStatus::Error("test error".to_string())).await;
        assert!(result.is_ok());

        let status = registry.get_model_status("test1").await;
        assert!(matches!(status, Some(ModelStatus::Error(_))));
    }

    #[tokio::test]
    async fn test_model_metrics() {
        let registry = ModelRegistry::new();
        let config = create_test_model_config("test1", EmbedderType::Local);

        registry.register_model(config).await.unwrap();

        // 更新指标
        let result = registry.update_model_metrics("test1", 100.0, true).await;
        assert!(result.is_ok());

        let metrics = registry.get_model_metrics("test1").await;
        assert!(metrics.is_some());
        let metrics = metrics.unwrap();
        assert_eq!(metrics.total_requests, 1);
        assert_eq!(metrics.successful_requests, 1);
        assert_eq!(metrics.avg_response_time_ms, 100.0);

        // 添加失败请求
        registry.update_model_metrics("test1", 200.0, false).await.unwrap();
        let metrics = registry.get_model_metrics("test1").await.unwrap();
        assert_eq!(metrics.total_requests, 2);
        assert_eq!(metrics.failed_requests, 1);
        assert_eq!(metrics.error_rate, 0.5);
    }

    #[tokio::test]
    async fn test_model_filtering() {
        let registry = ModelRegistry::new();

        // 注册不同类型的模型
        let config1 = create_test_model_config("local1", EmbedderType::Local);
        let config2 = create_test_model_config("openai1", EmbedderType::OpenAI);
        let config3 = create_test_model_config("local2", EmbedderType::Local);

        registry.register_model(config1).await.unwrap();
        registry.register_model(config2).await.unwrap();
        registry.register_model(config3).await.unwrap();

        // 设置不同状态
        registry.update_model_status("local1", ModelStatus::Available).await.unwrap();
        registry.update_model_status("openai1", ModelStatus::Error("test".to_string())).await.unwrap();
        registry.update_model_status("local2", ModelStatus::Available).await.unwrap();

        // 按状态筛选
        let available_models = registry.list_models_by_status(ModelStatus::Available).await;
        assert_eq!(available_models.len(), 2);
        assert!(available_models.contains(&"local1".to_string()));
        assert!(available_models.contains(&"local2".to_string()));

        // 按标签筛选
        let tagged_models = registry.list_models_by_tag("test", "true").await;
        assert_eq!(tagged_models.len(), 3);
    }

    #[tokio::test]
    async fn test_model_selection() {
        let registry = ModelRegistry::new();

        // 注册多个模型
        let config1 = create_test_model_config("fast", EmbedderType::Local)
            .with_priority(1)
            .with_weight(2.0);
        let config2 = create_test_model_config("slow", EmbedderType::Local)
            .with_priority(2)
            .with_weight(1.0);

        registry.register_model(config1).await.unwrap();
        registry.register_model(config2).await.unwrap();

        // 设置状态和指标
        registry.update_model_status("fast", ModelStatus::Available).await.unwrap();
        registry.update_model_status("slow", ModelStatus::Available).await.unwrap();
        registry.update_model_metrics("fast", 50.0, true).await.unwrap();
        registry.update_model_metrics("slow", 150.0, true).await.unwrap();

        // 测试优先级选择
        let criteria = ModelSelectionCriteria {
            selection_strategy: ModelSelectionStrategy::Priority,
            ..Default::default()
        };
        let best_model = registry.get_best_model(&criteria).await;
        assert_eq!(best_model, Some("fast".to_string()));

        // 测试最低延迟选择
        let criteria = ModelSelectionCriteria {
            selection_strategy: ModelSelectionStrategy::LowestLatency,
            ..Default::default()
        };
        let best_model = registry.get_best_model(&criteria).await;
        assert_eq!(best_model, Some("fast".to_string()));
    }

    #[tokio::test]
    async fn test_registry_summary() {
        let registry = ModelRegistry::new();

        // 注册不同状态的模型
        let config1 = create_test_model_config("model1", EmbedderType::Local);
        let config2 = create_test_model_config("model2", EmbedderType::OpenAI);

        registry.register_model(config1).await.unwrap();
        registry.register_model(config2).await.unwrap();

        registry.update_model_status("model1", ModelStatus::Available).await.unwrap();
        registry.update_model_status("model2", ModelStatus::Error("test".to_string())).await.unwrap();

        let summary = registry.get_registry_summary().await;
        assert_eq!(summary.total_models, 2);
        assert_eq!(summary.available_models, 1);
        assert_eq!(summary.error_models, 1);
        assert_eq!(summary.models_by_type.get("local"), Some(&1));
        assert_eq!(summary.models_by_type.get("openai"), Some(&1));
    }

    #[test]
    fn test_model_metrics_calculations() {
        let mut metrics = ModelMetrics::default();

        // 添加成功请求
        metrics.update(100.0, true);
        assert_eq!(metrics.total_requests, 1);
        assert_eq!(metrics.successful_requests, 1);
        assert_eq!(metrics.avg_response_time_ms, 100.0);
        assert_eq!(metrics.error_rate, 0.0);

        // 添加失败请求
        metrics.update(200.0, false);
        assert_eq!(metrics.total_requests, 2);
        assert_eq!(metrics.failed_requests, 1);
        assert_eq!(metrics.avg_response_time_ms, 150.0);
        assert_eq!(metrics.error_rate, 0.5);

        // 计算吞吐量
        metrics.calculate_throughput(60.0);
        assert_eq!(metrics.throughput_rps, 2.0 / 60.0);
    }

    #[test]
    fn test_model_config_builder() {
        let config = ModelConfig::new(
            "test".to_string(),
            EmbedderType::Local,
            "test-model".to_string(),
            768,
        )
        .with_priority(5)
        .with_weight(2.5)
        .with_tag("env".to_string(), "test".to_string())
        .with_tag("version".to_string(), "1.0".to_string());

        assert_eq!(config.model_id, "test");
        assert_eq!(config.priority, 5);
        assert_eq!(config.weight, 2.5);
        assert_eq!(config.tags.get("env"), Some(&"test".to_string()));
        assert_eq!(config.tags.get("version"), Some(&"1.0".to_string()));
    }
}
