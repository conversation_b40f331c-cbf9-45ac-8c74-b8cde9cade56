//! # 关键词搜索引擎
//!
//! 提供基于关键词的全文搜索功能。

use crate::{
    error::Result,
    parser::CodeChunk,
    knowledge_base::SearchResult,
};
use rusqlite::{Connection, Transaction};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 搜索算法类型
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Serialize, Deserialize)]
pub enum SearchAlgorithm {
    /// 简单频率匹配
    SimpleFrequency,
    /// TF-IDF 算法
    TfIdf,
    /// BM25 算法
    BM25,
}

/// 搜索配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchConfig {
    /// 搜索算法
    pub algorithm: SearchAlgorithm,
    /// BM25 参数 k1
    pub bm25_k1: f64,
    /// BM25 参数 b
    pub bm25_b: f64,
    /// 是否启用模糊匹配
    pub enable_fuzzy_match: bool,
    /// 模糊匹配阈值 (0.0-1.0)
    pub fuzzy_threshold: f64,
    /// 是否启用同义词扩展
    pub enable_synonym_expansion: bool,
}

impl Default for SearchConfig {
    fn default() -> Self {
        Self {
            algorithm: SearchAlgorithm::BM25,
            bm25_k1: 1.2,
            bm25_b: 0.75,
            enable_fuzzy_match: true,
            fuzzy_threshold: 0.8,
            enable_synonym_expansion: true,
        }
    }
}

/// 关键词搜索引擎
pub struct KeywordSearchEngine {
    connection: Arc<RwLock<Connection>>,
    /// 搜索配置
    config: SearchConfig,
    /// 同义词词典
    synonyms: HashMap<String, Vec<String>>,
    /// 文档统计信息
    doc_stats: DocumentStats,
}

/// 文档统计信息
#[derive(Debug, Clone, Default)]
struct DocumentStats {
    /// 总文档数
    total_docs: usize,
    /// 平均文档长度
    avg_doc_length: f64,
    /// 词汇表大小
    vocabulary_size: usize,
}

impl KeywordSearchEngine {
    /// 创建新的关键词搜索引擎
    pub async fn new(connection: Arc<RwLock<Connection>>) -> Result<Self> {
        let conn = connection.write().await;
        Self::init_tables(&conn).await?;
        drop(conn);

        Ok(Self {
            connection,
            config: SearchConfig::default(),
            synonyms: Self::load_default_synonyms(),
            doc_stats: DocumentStats::default(),
        })
    }

    /// 为代码块建立关键词索引
    pub async fn index_chunk(
        &self,
        tx: &Transaction<'_>,
        chunk: &CodeChunk,
    ) -> Result<()> {
        // 从代码内容中提取关键词
        let keywords = self.extract_keywords(&chunk.content, &chunk.language);
        
        // 添加符号名称作为关键词
        let mut all_keywords = keywords;
        if let Some(ref symbol_name) = chunk.symbol_name {
            all_keywords.insert(symbol_name.clone(), 10); // 符号名称权重更高
        }

        // 将关键词插入数据库
        for (keyword, frequency) in all_keywords {
            tx.execute(
                r#"
                INSERT OR REPLACE INTO keywords (chunk_id, keyword, frequency)
                VALUES (?1, ?2, ?3)
                "#,
                rusqlite::params![chunk.id, keyword, frequency],
            )?;
        }

        Ok(())
    }

    /// 加载默认同义词词典
    fn load_default_synonyms() -> HashMap<String, Vec<String>> {
        let mut synonyms = HashMap::new();

        // 编程语言相关同义词
        synonyms.insert("function".to_string(), vec!["func".to_string(), "method".to_string(), "procedure".to_string()]);
        synonyms.insert("variable".to_string(), vec!["var".to_string(), "field".to_string(), "property".to_string()]);
        synonyms.insert("class".to_string(), vec!["struct".to_string(), "type".to_string(), "interface".to_string()]);
        synonyms.insert("array".to_string(), vec!["list".to_string(), "vector".to_string(), "slice".to_string()]);
        synonyms.insert("string".to_string(), vec!["str".to_string(), "text".to_string()]);
        synonyms.insert("number".to_string(), vec!["int".to_string(), "float".to_string(), "integer".to_string()]);
        synonyms.insert("boolean".to_string(), vec!["bool".to_string(), "flag".to_string()]);
        synonyms.insert("error".to_string(), vec!["exception".to_string(), "failure".to_string()]);
        synonyms.insert("return".to_string(), vec!["result".to_string(), "output".to_string()]);
        synonyms.insert("parameter".to_string(), vec!["param".to_string(), "argument".to_string(), "arg".to_string()]);

        synonyms
    }

    /// 更新搜索配置
    pub fn update_config(&mut self, config: SearchConfig) {
        self.config = config;
    }

    /// 添加同义词
    pub fn add_synonym(&mut self, word: String, synonyms: Vec<String>) {
        self.synonyms.insert(word, synonyms);
    }

    /// 扩展查询词（包含同义词）
    fn expand_query_terms(&self, terms: &[String]) -> Vec<String> {
        if !self.config.enable_synonym_expansion {
            return terms.to_vec();
        }

        let mut expanded = HashSet::new();
        for term in terms {
            expanded.insert(term.clone());

            // 添加同义词
            if let Some(synonyms) = self.synonyms.get(term) {
                for synonym in synonyms {
                    expanded.insert(synonym.clone());
                }
            }
        }

        expanded.into_iter().collect()
    }

    /// 搜索关键词（使用配置的算法）
    pub async fn search_keywords(
        &self,
        keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        match self.config.algorithm {
            SearchAlgorithm::SimpleFrequency => self.search_simple_frequency(keywords, top_k).await,
            SearchAlgorithm::TfIdf => self.search_tf_idf(keywords, top_k).await,
            SearchAlgorithm::BM25 => self.search_bm25(keywords, top_k).await,
        }
    }

    /// 简单频率搜索（原有实现）
    async fn search_simple_frequency(
        &self,
        keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        if keywords.is_empty() {
            return Ok(Vec::new());
        }

        let conn = self.connection.read().await;
        
        // 构建查询语句
        let placeholders: Vec<String> = keywords.iter().map(|_| "?".to_string()).collect();
        let query = format!(
            r#"
            SELECT k.chunk_id, c.file_path, c.content, c.language, 
                   c.symbol_name, c.start_line, c.end_line,
                   SUM(k.frequency) as total_score
            FROM keywords k
            INNER JOIN chunks c ON k.chunk_id = c.id
            WHERE k.keyword IN ({})
            GROUP BY k.chunk_id
            ORDER BY total_score DESC
            LIMIT ?
            "#,
            placeholders.join(", ")
        );

        let mut stmt = conn.prepare(&query)?;
        
        // 准备参数
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();
        for keyword in keywords {
            params.push(keyword);
        }
        params.push(&top_k);

        let rows = stmt.query_map(&params[..], |row| {
            Ok(SearchResult {
                chunk_id: row.get(0)?,
                file_path: row.get(1)?,
                content: row.get(2)?,
                language: row.get(3)?,
                symbol_name: row.get::<_, Option<String>>(4)?,
                start_line: row.get(5)?,
                end_line: row.get(6)?,
                score: row.get::<_, f64>(7)? as f32 / 100.0, // 归一化分数
            })
        })?;

        let mut results = Vec::new();
        for row in rows {
            results.push(row?);
        }

        Ok(results)
    }

    /// BM25 算法搜索
    async fn search_bm25(
        &self,
        keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        if keywords.is_empty() {
            return Ok(Vec::new());
        }

        // 扩展查询词（包含同义词）
        let expanded_keywords = self.expand_query_terms(keywords);

        let conn = self.connection.read().await;

        // 获取文档统计信息
        let total_docs: f64 = conn.query_row(
            "SELECT COUNT(DISTINCT chunk_id) FROM keywords",
            [],
            |row| row.get::<_, i64>(0)
        )? as f64;

        let avg_doc_length: f64 = conn.query_row(
            r#"
            SELECT AVG(doc_length) FROM (
                SELECT chunk_id, SUM(frequency) as doc_length
                FROM keywords
                GROUP BY chunk_id
            )
            "#,
            [],
            |row| row.get::<_, f64>(0)
        ).unwrap_or(100.0);

        // 为每个关键词计算 BM25 分数
        let mut chunk_scores: HashMap<String, f64> = HashMap::new();

        for keyword in &expanded_keywords {
            // 获取包含该关键词的文档数
            let docs_with_term: f64 = conn.query_row(
                "SELECT COUNT(DISTINCT chunk_id) FROM keywords WHERE keyword = ?1",
                [keyword],
                |row| row.get::<_, i64>(0)
            )? as f64;

            if docs_with_term == 0.0 {
                continue;
            }

            // 计算 IDF
            let idf = ((total_docs - docs_with_term + 0.5) / (docs_with_term + 0.5)).ln();

            // 获取每个文档中该词的频率
            let mut stmt = conn.prepare(
                r#"
                SELECT k.chunk_id, k.frequency,
                       (SELECT SUM(frequency) FROM keywords WHERE chunk_id = k.chunk_id) as doc_length
                FROM keywords k
                WHERE k.keyword = ?1
                "#
            )?;

            let rows = stmt.query_map([keyword], |row| {
                Ok((
                    row.get::<_, String>(0)?,  // chunk_id
                    row.get::<_, f64>(1)?,     // frequency
                    row.get::<_, f64>(2)?,     // doc_length
                ))
            })?;

            for row in rows {
                let (chunk_id, tf, doc_length) = row?;

                // 计算 BM25 分数
                let normalized_tf = (tf * (self.config.bm25_k1 + 1.0)) /
                    (tf + self.config.bm25_k1 * (1.0 - self.config.bm25_b +
                     self.config.bm25_b * (doc_length / avg_doc_length)));

                let bm25_score = idf * normalized_tf;

                *chunk_scores.entry(chunk_id).or_insert(0.0) += bm25_score;
            }
        }

        // 获取排序后的结果
        let mut scored_chunks: Vec<(String, f64)> = chunk_scores.into_iter().collect();
        scored_chunks.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        scored_chunks.truncate(top_k);

        // 获取详细信息
        let mut results = Vec::new();
        for (chunk_id, score) in scored_chunks {
            let result = conn.query_row(
                r#"
                SELECT file_path, content, language, symbol_name, start_line, end_line
                FROM chunks WHERE id = ?1
                "#,
                [&chunk_id],
                |row| {
                    Ok(SearchResult {
                        chunk_id: chunk_id.clone(),
                        file_path: row.get(0)?,
                        content: row.get(1)?,
                        language: row.get(2)?,
                        symbol_name: row.get::<_, Option<String>>(3)?,
                        start_line: row.get(4)?,
                        end_line: row.get(5)?,
                        score: score as f32,
                    })
                }
            )?;
            results.push(result);
        }

        Ok(results)
    }

    /// TF-IDF 算法搜索
    async fn search_tf_idf(
        &self,
        keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        if keywords.is_empty() {
            return Ok(Vec::new());
        }

        // 扩展查询词（包含同义词）
        let expanded_keywords = self.expand_query_terms(keywords);

        let conn = self.connection.read().await;

        // 获取总文档数
        let total_docs: f64 = conn.query_row(
            "SELECT COUNT(DISTINCT chunk_id) FROM keywords",
            [],
            |row| row.get::<_, i64>(0)
        )? as f64;

        let mut chunk_scores: HashMap<String, f64> = HashMap::new();

        for keyword in &expanded_keywords {
            // 获取包含该关键词的文档数
            let docs_with_term: f64 = conn.query_row(
                "SELECT COUNT(DISTINCT chunk_id) FROM keywords WHERE keyword = ?1",
                [keyword],
                |row| row.get::<_, i64>(0)
            )? as f64;

            if docs_with_term == 0.0 {
                continue;
            }

            // 计算 IDF
            let idf = (total_docs / docs_with_term).ln();

            // 获取每个文档中该词的 TF
            let mut stmt = conn.prepare(
                r#"
                SELECT k.chunk_id, k.frequency,
                       (SELECT SUM(frequency) FROM keywords WHERE chunk_id = k.chunk_id) as total_terms
                FROM keywords k
                WHERE k.keyword = ?1
                "#
            )?;

            let rows = stmt.query_map([keyword], |row| {
                Ok((
                    row.get::<_, String>(0)?,  // chunk_id
                    row.get::<_, f64>(1)?,     // frequency
                    row.get::<_, f64>(2)?,     // total_terms
                ))
            })?;

            for row in rows {
                let (chunk_id, frequency, total_terms) = row?;

                // 计算 TF
                let tf = frequency / total_terms;

                // 计算 TF-IDF 分数
                let tf_idf_score = tf * idf;

                *chunk_scores.entry(chunk_id).or_insert(0.0) += tf_idf_score;
            }
        }

        // 获取排序后的结果
        let mut scored_chunks: Vec<(String, f64)> = chunk_scores.into_iter().collect();
        scored_chunks.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        scored_chunks.truncate(top_k);

        // 获取详细信息
        let mut results = Vec::new();
        for (chunk_id, score) in scored_chunks {
            let result = conn.query_row(
                r#"
                SELECT file_path, content, language, symbol_name, start_line, end_line
                FROM chunks WHERE id = ?1
                "#,
                [&chunk_id],
                |row| {
                    Ok(SearchResult {
                        chunk_id: chunk_id.clone(),
                        file_path: row.get(0)?,
                        content: row.get(1)?,
                        language: row.get(2)?,
                        symbol_name: row.get::<_, Option<String>>(3)?,
                        start_line: row.get(4)?,
                        end_line: row.get(5)?,
                        score: score as f32,
                    })
                }
            )?;
            results.push(result);
        }

        Ok(results)
    }

    /// 从代码内容中提取关键词
    fn extract_keywords(&self, content: &str, language: &str) -> HashMap<String, i32> {
        let mut keywords = HashMap::new();
        
        // 按语言定制关键词提取策略
        match language {
            "rust" => self.extract_rust_keywords(content, &mut keywords),
            "javascript" | "typescript" => self.extract_js_keywords(content, &mut keywords),
            "python" => self.extract_python_keywords(content, &mut keywords),
            "java" => self.extract_java_keywords(content, &mut keywords),
            _ => self.extract_generic_keywords(content, &mut keywords),
        }

        // 通用关键词提取
        self.extract_generic_keywords(content, &mut keywords);
        
        keywords
    }

    /// 提取 Rust 关键词
    fn extract_rust_keywords(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        let rust_keywords = [
            "fn", "struct", "enum", "impl", "trait", "use", "mod", "pub", "let", "mut",
            "match", "if", "else", "while", "for", "loop", "break", "continue", "return",
            "const", "static", "unsafe", "async", "await",
        ];

        for keyword in &rust_keywords {
            let count = content.matches(keyword).count() as i32;
            if count > 0 {
                keywords.insert(keyword.to_string(), count);
            }
        }

        // 提取标识符
        self.extract_identifiers(content, keywords);
    }

    /// 提取 JavaScript/TypeScript 关键词
    fn extract_js_keywords(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        let js_keywords = [
            "function", "const", "let", "var", "class", "extends", "export", "import",
            "if", "else", "while", "for", "do", "switch", "case", "break", "continue",
            "return", "try", "catch", "finally", "throw", "async", "await",
        ];

        for keyword in &js_keywords {
            let count = content.matches(keyword).count() as i32;
            if count > 0 {
                keywords.insert(keyword.to_string(), count);
            }
        }

        self.extract_identifiers(content, keywords);
    }

    /// 提取 Python 关键词
    fn extract_python_keywords(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        let python_keywords = [
            "def", "class", "import", "from", "if", "elif", "else", "while", "for",
            "break", "continue", "return", "try", "except", "finally", "raise",
            "with", "as", "lambda", "yield", "global", "nonlocal",
        ];

        for keyword in &python_keywords {
            let count = content.matches(keyword).count() as i32;
            if count > 0 {
                keywords.insert(keyword.to_string(), count);
            }
        }

        self.extract_identifiers(content, keywords);
    }

    /// 提取 Java 关键词
    fn extract_java_keywords(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        let java_keywords = [
            "public", "private", "protected", "static", "final", "abstract", "class",
            "interface", "extends", "implements", "package", "import", "if", "else",
            "while", "for", "do", "switch", "case", "break", "continue", "return",
            "try", "catch", "finally", "throw", "throws",
        ];

        for keyword in &java_keywords {
            let count = content.matches(keyword).count() as i32;
            if count > 0 {
                keywords.insert(keyword.to_string(), count);
            }
        }

        self.extract_identifiers(content, keywords);
    }

    /// 提取通用关键词
    fn extract_generic_keywords(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        // 提取长度大于 2 的单词
        for word in content.split_whitespace() {
            let clean_word = word.trim_matches(|c: char| !c.is_alphanumeric())
                .to_lowercase();
            
            if clean_word.len() > 2 && clean_word.chars().all(|c| c.is_alphabetic()) {
                *keywords.entry(clean_word).or_insert(0) += 1;
            }
        }
    }

    /// 提取标识符（函数名、变量名等）
    fn extract_identifiers(&self, content: &str, keywords: &mut HashMap<String, i32>) {
        // 简化的标识符提取，实际实现可能需要更复杂的解析
        for line in content.lines() {
            // 查找可能的函数定义
            if let Some(func_name) = self.extract_function_name(line) {
                *keywords.entry(func_name).or_insert(0) += 5; // 函数名权重更高
            }
            
            // 查找可能的变量名
            for var_name in self.extract_variable_names(line) {
                *keywords.entry(var_name).or_insert(0) += 2;
            }
        }
    }

    /// 提取函数名
    fn extract_function_name(&self, line: &str) -> Option<String> {
        // 简化的函数名提取
        if line.contains("fn ") || line.contains("function ") || line.contains("def ") {
            for word in line.split_whitespace() {
                if word.len() > 2 && word.chars().all(|c| c.is_alphanumeric() || c == '_') {
                    if !["fn", "function", "def", "public", "private"].contains(&word) {
                        return Some(word.to_string());
                    }
                }
            }
        }
        None
    }

    /// 提取变量名
    fn extract_variable_names(&self, line: &str) -> Vec<String> {
        let mut names = Vec::new();
        
        // 简化的变量名提取
        if line.contains("let ") || line.contains("const ") || line.contains("var ") {
            for word in line.split_whitespace() {
                if word.len() > 1 && word.chars().all(|c| c.is_alphanumeric() || c == '_') {
                    if !["let", "const", "var", "mut"].contains(&word) {
                        names.push(word.to_string());
                    }
                }
            }
        }
        
        names
    }

    /// 初始化数据库表
    async fn init_tables(conn: &Connection) -> Result<()> {
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chunk_id TEXT NOT NULL,
                keyword TEXT NOT NULL,
                frequency INTEGER NOT NULL DEFAULT 1,
                FOREIGN KEY (chunk_id) REFERENCES chunks (id) ON DELETE CASCADE
            )
            "#,
            [],
        )?;

        // 创建索引
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_keywords_chunk ON keywords (chunk_id)",
            [],
        )?;
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords (keyword)",
            [],
        )?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rusqlite::Connection;
    use std::sync::Arc;
    use tokio::sync::RwLock;

    #[test]
    fn test_keyword_extraction() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let rt = tokio::runtime::Runtime::new().unwrap();
        let engine = rt.block_on(KeywordSearchEngine::new(conn)).unwrap();
        
        let content = "fn hello_world() { println!(\"Hello\"); }";
        let keywords = engine.extract_keywords(content, "rust");
        
        assert!(keywords.contains_key("fn"));
        assert!(keywords.contains_key("hello"));
    }

    #[test]
    fn test_function_name_extraction() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let rt = tokio::runtime::Runtime::new().unwrap();
        let engine = rt.block_on(KeywordSearchEngine::new(conn)).unwrap();
        
        let func_name = engine.extract_function_name("fn hello_world() {");
        assert_eq!(func_name, Some("hello_world".to_string()));
    }
}