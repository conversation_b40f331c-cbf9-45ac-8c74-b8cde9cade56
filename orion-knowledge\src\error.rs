//! # 知识库错误类型
//!
//! 定义知识库系统中的所有错误类型和错误处理机制。

use thiserror::Error;

/// 知识库系统错误类型
#[derive(Error, Debug)]
pub enum KnowledgeError {
    /// 解析错误
    #[error("解析错误: {message}")]
    ParseError { message: String },

    /// 存储错误
    #[error("存储错误: {message}")]
    StorageError { message: String },

    /// 嵌入模型错误
    #[error("嵌入模型错误: {message}")]
    EmbeddingError { message: String },

    /// 查询错误
    #[error("查询错误: {message}")]
    QueryError { message: String },

    /// 上下文错误
    #[error("上下文处理错误: {message}")]
    ContextError { message: String },

    /// 配置错误
    #[error("配置错误: {message}")]
    ConfigError { message: String },

    /// IO 错误
    #[error("IO 错误: {source}")]
    IoError {
        #[from]
        source: std::io::Error,
    },

    /// 数据库错误
    #[error("数据库错误: {source}")]
    DatabaseError {
        #[from]
        source: rusqlite::Error,
    },

    /// 文件系统遍历错误
    #[error("文件系统遍历错误: {source}")]
    WalkdirError {
        #[from]
        source: walkdir::Error,
    },

    /// 序列化错误
    #[error("序列化错误: {source}")]
    SerializationError {
        #[from]
        source: serde_json::Error,
    },

    /// 网络错误
    #[error("网络错误: {message}")]
    NetworkError { message: String },

    /// 未知错误
    #[error("未知错误: {message}")]
    Unknown { message: String },
}

impl KnowledgeError {
    /// 创建解析错误
    pub fn parse_error(message: impl Into<String>) -> Self {
        Self::ParseError {
            message: message.into(),
        }
    }

    /// 创建存储错误
    pub fn storage_error(message: impl Into<String>) -> Self {
        Self::StorageError {
            message: message.into(),
        }
    }

    /// 创建嵌入模型错误
    pub fn embedding_error(message: impl Into<String>) -> Self {
        Self::EmbeddingError {
            message: message.into(),
        }
    }

    /// 创建查询错误
    pub fn query_error(message: impl Into<String>) -> Self {
        Self::QueryError {
            message: message.into(),
        }
    }

    /// 创建上下文错误
    pub fn context_error(message: impl Into<String>) -> Self {
        Self::ContextError {
            message: message.into(),
        }
    }

    /// 创建配置错误
    pub fn config_error(message: impl Into<String>) -> Self {
        Self::ConfigError {
            message: message.into(),
        }
    }

    /// 创建网络错误
    pub fn network_error(message: impl Into<String>) -> Self {
        Self::NetworkError {
            message: message.into(),
        }
    }

    /// 创建未知错误
    pub fn unknown(message: impl Into<String>) -> Self {
        Self::Unknown {
            message: message.into(),
        }
    }
}

/// 知识库系统结果类型
pub type Result<T> = std::result::Result<T, KnowledgeError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = KnowledgeError::parse_error("测试错误");
        assert!(matches!(error, KnowledgeError::ParseError { .. }));
    }

    #[test]
    fn test_error_display() {
        let error = KnowledgeError::storage_error("存储失败");
        let error_string = format!("{}", error);
        assert!(error_string.contains("存储错误"));
        assert!(error_string.contains("存储失败"));
    }
}