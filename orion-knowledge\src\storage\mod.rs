//! # 存储模块
//!
//! 提供向量数据库和关键词搜索功能。

pub mod hybrid_search;
pub mod index_manager;
pub mod keyword_search;
pub mod vector_db;



use crate::{
    config::DatabaseConfig,
    error::Result,
    parser::CodeChunk,
    embedding::Embedding,
    knowledge_base::SearchResult,
};
use rusqlite::{Connection, OptionalExtension};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 存储统计信息
#[derive(Debug, Clone)]
pub struct StorageStats {
    pub total_chunks: usize,
    pub total_files: usize,
    pub total_languages: usize,
    pub database_size: u64,
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 向量数据库
pub struct VectorDatabase {
    config: DatabaseConfig,
    connection: Arc<RwLock<Connection>>,
    vector_index: vector_db::VectorIndex,
    keyword_engine: keyword_search::KeywordSearchEngine,
    index_manager: index_manager::IndexManager,
}

impl VectorDatabase {
    /// 创建新的向量数据库实例
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        // 确保数据库目录存在
        if let Some(parent) = config.path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // 创建数据库连接
        let conn = Connection::open(&config.path)?;
        
        // 启用 WAL 模式
        if config.enable_wal {
            conn.execute_batch("PRAGMA journal_mode = WAL")?;
        }
        
        // 设置缓存大小
        let cache_size = -(config.cache_size_mb as i64 * 1024); // 负值表示KB
        conn.execute_batch(&format!("PRAGMA cache_size = {}", cache_size))?;

        let connection = Arc::new(RwLock::new(conn));
        
        // 初始化数据库表结构
        {
            let conn_guard = connection.read().await;
            Self::init_tables(&conn_guard).await?;
        }
        
        // 初始化各个组件
        let vector_index = vector_db::VectorIndex::new(connection.clone()).await?;
        let keyword_engine = keyword_search::KeywordSearchEngine::new(connection.clone()).await?;
        let index_manager = index_manager::IndexManager::new(connection.clone()).await?;

        Ok(Self {
            config: config.clone(),
            connection,
            vector_index,
            keyword_engine,
            index_manager,
        })
    }

    /// 插入代码块和嵌入向量
    pub async fn insert_chunks_with_embeddings(
        &self,
        chunks: &[CodeChunk],
        embeddings: &[Embedding],
    ) -> Result<()> {
        if chunks.len() != embeddings.len() {
            return Err(crate::KnowledgeError::storage_error(
                "代码块数量与嵌入向量数量不匹配"
            ));
        }

        let conn = self.connection.write().await;
        let tx = conn.unchecked_transaction()?;

        for (chunk, embedding) in chunks.iter().zip(embeddings.iter()) {
            // 插入代码块基本信息
            tx.execute(
                r#"
                INSERT OR REPLACE INTO chunks (
                    id, file_path, content, language, chunk_type,
                    start_line, end_line, symbol_name, parent_symbol,
                    dependencies, metadata, created_at
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12)
                "#,
                rusqlite::params![
                    chunk.id,
                    chunk.file_path.to_string_lossy(),
                    chunk.content,
                    chunk.language,
                    serde_json::to_string(&chunk.chunk_type)?,
                    chunk.start_line,
                    chunk.end_line,
                    chunk.symbol_name,
                    chunk.parent_symbol,
                    serde_json::to_string(&chunk.dependencies)?,
                    serde_json::to_string(&chunk.metadata)?,
                    chunk.created_at.timestamp(),
                ],
            )?;

            // 插入向量数据
            self.vector_index.insert_vector(&tx, &chunk.id, embedding).await?;
            
            // 建立关键词索引
            self.keyword_engine.index_chunk(&tx, chunk).await?;
        }

        tx.commit()?;
        
        // 更新索引管理
        self.index_manager.update_stats(chunks.len()).await?;

        Ok(())
    }

    /// 向量相似度搜索
    pub async fn search_similar(
        &self,
        query_embedding: &Embedding,
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        self.vector_index.search_similar(query_embedding, top_k).await
    }

    /// 关键词搜索
    pub async fn search_keywords(
        &self,
        keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        self.keyword_engine.search_keywords(keywords, top_k).await
    }

    /// 根据文件路径搜索
    pub async fn search_by_file(&self, file_path: &str) -> Result<Vec<SearchResult>> {
        let conn = self.connection.read().await;
        let mut stmt = conn.prepare(
            r#"
            SELECT id, file_path, content, language, chunk_type, 
                   start_line, end_line, symbol_name
            FROM chunks 
            WHERE file_path LIKE ?1
            ORDER BY start_line
            "#,
        )?;

        let rows = stmt.query_map([format!("%{}", file_path)], |row| {
            Ok(SearchResult {
                chunk_id: row.get(0)?,
                file_path: row.get(1)?,
                content: row.get(2)?,
                language: row.get(3)?,
                symbol_name: row.get::<_, Option<String>>(7)?,
                start_line: row.get(5)?,
                end_line: row.get(6)?,
                score: 1.0, // 文件搜索默认分数
            })
        })?;

        let mut results = Vec::new();
        for row in rows {
            results.push(row?);
        }

        Ok(results)
    }

    /// 根据符号名称搜索
    pub async fn search_by_symbol(&self, symbol_name: &str) -> Result<Vec<SearchResult>> {
        let conn = self.connection.read().await;
        let mut stmt = conn.prepare(
            r#"
            SELECT id, file_path, content, language, chunk_type, 
                   start_line, end_line, symbol_name
            FROM chunks 
            WHERE symbol_name LIKE ?1 OR parent_symbol LIKE ?1
            ORDER BY symbol_name
            "#,
        )?;

        let rows = stmt.query_map([format!("%{}", symbol_name)], |row| {
            Ok(SearchResult {
                chunk_id: row.get(0)?,
                file_path: row.get(1)?,
                content: row.get(2)?,
                language: row.get(3)?,
                symbol_name: row.get::<_, Option<String>>(7)?,
                start_line: row.get(5)?,
                end_line: row.get(6)?,
                score: 1.0,
            })
        })?;

        let mut results = Vec::new();
        for row in rows {
            results.push(row?);
        }

        Ok(results)
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> Result<StorageStats> {
        let conn = self.connection.read().await;
        
        let total_chunks: usize = conn
            .query_row("SELECT COUNT(*) FROM chunks", [], |row| row.get(0))?;
            
        let total_files: usize = conn
            .query_row("SELECT COUNT(DISTINCT file_path) FROM chunks", [], |row| row.get(0))?;
            
        let total_languages: usize = conn
            .query_row("SELECT COUNT(DISTINCT language) FROM chunks", [], |row| row.get(0))?;

        let last_updated: Option<i64> = conn
            .query_row("SELECT MAX(created_at) FROM chunks", [], |row| row.get(0))
            .optional()?
            .flatten();

        let last_updated = last_updated
            .map(|ts| chrono::DateTime::from_timestamp(ts, 0).unwrap_or_else(chrono::Utc::now))
            .unwrap_or_else(chrono::Utc::now);

        // 获取数据库文件大小
        let database_size = std::fs::metadata(&self.config.path)
            .map(|m| m.len())
            .unwrap_or(0);

        Ok(StorageStats {
            total_chunks,
            total_files,
            total_languages,
            database_size,
            last_updated,
        })
    }

    /// 清理数据库
    pub async fn clear(&self) -> Result<()> {
        let conn = self.connection.write().await;
        conn.execute("DELETE FROM chunks", [])?;
        conn.execute("DELETE FROM vectors", [])?;
        conn.execute("DELETE FROM keywords", [])?;
        conn.execute("VACUUM", [])?;
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize(&self) -> Result<()> {
        let conn = self.connection.write().await;
        conn.execute("ANALYZE", [])?;
        conn.execute("PRAGMA optimize", [])?;
        Ok(())
    }

    /// 初始化数据库表结构
    async fn init_tables(conn: &Connection) -> Result<()> {
        // 创建代码块表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS chunks (
                id TEXT PRIMARY KEY,
                file_path TEXT NOT NULL,
                content TEXT NOT NULL,
                language TEXT NOT NULL,
                chunk_type TEXT NOT NULL,
                start_line INTEGER NOT NULL,
                end_line INTEGER NOT NULL,
                symbol_name TEXT,
                parent_symbol TEXT,
                dependencies TEXT, -- JSON 数组
                metadata TEXT,     -- JSON 对象
                created_at INTEGER NOT NULL
            )
            "#,
            [],
        )?;

        // 创建向量表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS vectors (
                chunk_id TEXT PRIMARY KEY,
                vector BLOB NOT NULL,
                dimension INTEGER NOT NULL,
                model_name TEXT NOT NULL,
                model_version TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (chunk_id) REFERENCES chunks (id) ON DELETE CASCADE
            )
            "#,
            [],
        )?;

        // 创建关键词索引表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS keywords (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chunk_id TEXT NOT NULL,
                keyword TEXT NOT NULL,
                frequency INTEGER NOT NULL DEFAULT 1,
                FOREIGN KEY (chunk_id) REFERENCES chunks (id) ON DELETE CASCADE
            )
            "#,
            [],
        )?;

        // 创建索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_chunks_file_path ON chunks (file_path)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_chunks_language ON chunks (language)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_chunks_symbol ON chunks (symbol_name)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_keywords_chunk ON keywords (chunk_id)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords (keyword)", [])?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_vector_database_creation() {
        let temp_db = NamedTempFile::new().unwrap();
        let config = DatabaseConfig {
            path: temp_db.path().to_path_buf(),
            pool_size: 1,
            query_timeout: 30,
            enable_wal: false,
            cache_size_mb: 16,
        };

        let db = VectorDatabase::new(&config).await;
        assert!(db.is_ok());
    }

    #[tokio::test]
    async fn test_database_stats() {
        let temp_db = NamedTempFile::new().unwrap();
        let config = DatabaseConfig {
            path: temp_db.path().to_path_buf(),
            pool_size: 1,
            query_timeout: 30,
            enable_wal: false,
            cache_size_mb: 16,
        };

        let db = VectorDatabase::new(&config).await.unwrap();
        let stats = db.get_stats().await.unwrap();
        
        assert_eq!(stats.total_chunks, 0);
        assert_eq!(stats.total_files, 0);
    }

    // 添加更多存储系统测试
    use crate::{
        embedding::{Embedding, ModelInfo},
        parser::CodeChunk,
        storage::{
            vector_db::VectorIndex,
            keyword_search::KeywordSearchEngine,
        },
    };
    use std::collections::HashMap;
    use std::path::PathBuf;

    /// 创建测试代码块
    fn create_test_chunk_helper(id: &str, content: &str, language: &str) -> CodeChunk {
        CodeChunk {
            id: id.to_string(),
            file_path: PathBuf::from(format!("test_{}.{}", id, language)),
            content: content.to_string(),
            language: language.to_string(),
            symbol_name: Some(format!("test_symbol_{}", id)),
            parent_symbol: None,
            start_line: 1,
            end_line: content.lines().count(),
            chunk_type: crate::parser::ChunkType::Function,
            dependencies: Vec::new(),
            metadata: HashMap::new(),
            created_at: chrono::Utc::now(),
        }
    }

    /// 创建测试嵌入向量
    fn create_test_embedding_helper(dimension: usize) -> Embedding {
        let vector: Vec<f32> = (0..dimension).map(|i| (i as f32) / (dimension as f32)).collect();

        Embedding {
            vector,
            dimension,
            model_info: ModelInfo {
                name: "test_model".to_string(),
                version: "1.0".to_string(),
                model_type: "test".to_string(),
            },
            created_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_vector_index_operations() {
        let temp_db = NamedTempFile::new().unwrap();
        let config = DatabaseConfig {
            path: temp_db.path().to_path_buf(),
            pool_size: 1,
            query_timeout: 30,
            enable_wal: false,
            cache_size_mb: 16,
        };

        let db = VectorDatabase::new(&config).await.unwrap();
        let vector_index = VectorIndex::new(db.connection.clone()).await.unwrap();

        // 创建测试数据
        let chunk = create_test_chunk_helper("test1", "fn hello() { println!(\"Hello\"); }", "rust");
        let embedding = create_test_embedding_helper(128);

        // 测试插入和搜索
        {
            let conn = db.connection.write().await;
            let tx = conn.unchecked_transaction().unwrap();

            // 插入代码块到数据库
            tx.execute(
                r#"
                INSERT INTO chunks (id, file_path, content, language, symbol_name, parent_symbol, start_line, end_line, chunk_type, created_at)
                VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)
                "#,
                rusqlite::params![
                    chunk.id,
                    chunk.file_path.to_string_lossy(),
                    chunk.content,
                    chunk.language,
                    chunk.symbol_name,
                    chunk.parent_symbol,
                    chunk.start_line,
                    chunk.end_line,
                    format!("{:?}", chunk.chunk_type),
                    chunk.created_at.timestamp(),
                ],
            ).unwrap();

            // 插入向量
            vector_index.insert_vector(&tx, &chunk.id, &embedding).await.unwrap();
            tx.commit().unwrap();
        }

        // 测试搜索
        let query_embedding = create_test_embedding_helper(128);
        let results = vector_index.search_similar(&query_embedding, 5).await.unwrap();

        assert!(!results.is_empty());
        assert_eq!(results[0].chunk_id, chunk.id);
    }

    #[tokio::test]
    async fn test_keyword_search_integration() {
        let temp_db = NamedTempFile::new().unwrap();
        let config = DatabaseConfig {
            path: temp_db.path().to_path_buf(),
            pool_size: 1,
            query_timeout: 30,
            enable_wal: false,
            cache_size_mb: 16,
        };

        let db = VectorDatabase::new(&config).await.unwrap();
        let keyword_engine = KeywordSearchEngine::new(db.connection.clone()).await.unwrap();

        // 创建测试数据
        let chunk = create_test_chunk_helper("test2", "function calculateSum(a, b) { return a + b; }", "javascript");

        // 插入数据
        {
            let conn = db.connection.write().await;
            let tx = conn.unchecked_transaction().unwrap();

            // 插入代码块到数据库
            tx.execute(
                r#"
                INSERT INTO chunks (id, file_path, content, language, symbol_name, parent_symbol, start_line, end_line, chunk_type, created_at)
                VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)
                "#,
                rusqlite::params![
                    chunk.id,
                    chunk.file_path.to_string_lossy(),
                    chunk.content,
                    chunk.language,
                    chunk.symbol_name,
                    chunk.parent_symbol,
                    chunk.start_line,
                    chunk.end_line,
                    format!("{:?}", chunk.chunk_type),
                    chunk.created_at.timestamp(),
                ],
            ).unwrap();
            keyword_engine.index_chunk(&tx, &chunk).await.unwrap();
            tx.commit().unwrap();
        }

        // 测试搜索
        let keywords = vec!["function".to_string(), "calculate".to_string()];
        let results = keyword_engine.search_keywords(&keywords, 5).await.unwrap();

        assert!(!results.is_empty());
        assert_eq!(results[0].chunk_id, chunk.id);
    }
}