//! # 知识库主类
//!
//! 提供知识库系统的统一入口和核心功能。

use crate::{
    config::Config,
    error::Result,
    parser::CodeParser,
    embedding::EmbeddingEngine,
    storage::VectorDatabase,
    query::QueryProcessor,
    context::ContextSynthesizer,
};
use std::path::Path;
use tracing::{info, warn, error};

/// 搜索结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SearchResult {
    /// 代码块 ID
    pub chunk_id: String,
    /// 文件路径
    pub file_path: String,
    /// 代码内容
    pub content: String,
    /// 相似度分数
    pub score: f32,
    /// 代码语言
    pub language: String,
    /// 函数/类名称
    pub symbol_name: Option<String>,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
}

/// 上下文信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct ContextInfo {
    /// 相关代码块
    pub chunks: Vec<SearchResult>,
    /// 合成的上下文
    pub synthesized_context: String,
    /// 上下文长度（tokens）
    pub token_count: usize,
    /// 置信度
    pub confidence: f32,
}

/// 知识库主类
pub struct KnowledgeBase {
    /// 配置
    config: Config,
    /// 代码解析器
    parser: CodeParser,
    /// 嵌入引擎
    embedding: EmbeddingEngine,
    /// 向量数据库
    storage: VectorDatabase,
    /// 查询处理器
    query_processor: QueryProcessor,
    /// 上下文合成器
    context_synthesizer: ContextSynthesizer,
}

impl KnowledgeBase {
    /// 创建新的知识库实例
    pub async fn new(config: Config) -> Result<Self> {
        info!("初始化知识库系统...");
        
        // 验证配置
        config.validate()?;
        
        // 初始化各个组件
        let parser = CodeParser::new(&config.parser)?;
        let embedding = EmbeddingEngine::new(&config.embedding).await?;
        let storage = VectorDatabase::new(&config.database).await?;
        let query_processor = QueryProcessor::new(&config.query)?;
        let context_synthesizer = ContextSynthesizer::new(&config.context)?;
        
        info!("知识库系统初始化完成");
        
        Ok(Self {
            config,
            parser,
            embedding,
            storage,
            query_processor,
            context_synthesizer,
        })
    }

    /// 从配置文件创建知识库
    pub async fn from_config_file(config_path: impl AsRef<Path>) -> Result<Self> {
        let config = Config::from_file(config_path)?;
        Self::new(config).await
    }

    /// 索引单个文件
    pub async fn index_file(&mut self, file_path: impl AsRef<Path>) -> Result<()> {
        let file_path = file_path.as_ref();
        info!("索引文件: {}", file_path.display());

        // 解析代码文件
        let chunks = self.parser.parse_file(file_path).await?;
        
        if chunks.is_empty() {
            warn!("文件 {} 没有解析出有效代码块", file_path.display());
            return Ok(());
        }

        info!("文件 {} 解析出 {} 个代码块", file_path.display(), chunks.len());

        // 批量计算嵌入向量
        let embeddings = self.embedding.embed_chunks(&chunks).await?;

        // 存储到向量数据库
        self.storage.insert_chunks_with_embeddings(&chunks, &embeddings).await?;

        info!("文件 {} 索引完成", file_path.display());
        Ok(())
    }

    /// 索引整个目录
    pub async fn index_directory(&mut self, dir_path: impl AsRef<Path>) -> Result<()> {
        let dir_path = dir_path.as_ref();
        info!("开始索引目录: {}", dir_path.display());

        // 获取目录中的所有代码文件
        let files = self.parser.discover_files(dir_path)?;
        info!("发现 {} 个代码文件", files.len());

        let mut success_count = 0;
        let mut error_count = 0;

        // 逐个处理文件
        for file_path in files {
            match self.index_file(&file_path).await {
                Ok(()) => {
                    success_count += 1;
                }
                Err(e) => {
                    error!("索引文件 {} 失败: {}", file_path.display(), e);
                    error_count += 1;
                }
            }
        }

        info!(
            "目录索引完成: 成功 {} 个文件, 失败 {} 个文件", 
            success_count, 
            error_count
        );

        Ok(())
    }

    /// 语义搜索
    pub async fn search(&self, query: &str) -> Result<Vec<SearchResult>> {
        info!("执行搜索查询: {}", query);

        // 处理查询
        let processed_query = self.query_processor.process_query(query).await?;

        // 计算查询向量
        let query_embedding = self.embedding.embed_query(&processed_query.text).await?;

        // 向量搜索
        let vector_results = self.storage
            .search_similar(&query_embedding, processed_query.top_k)
            .await?;

        // 关键词搜索（如果启用混合搜索）
        let keyword_results = if processed_query.enable_hybrid {
            self.storage
                .search_keywords(&processed_query.keywords, processed_query.top_k)
                .await?
        } else {
            Vec::new()
        };

        // 合并和排序结果
        let merged_results = self.query_processor
            .merge_results(vector_results, keyword_results, &processed_query)
            .await?;

        info!("搜索完成，返回 {} 个结果", merged_results.len());
        Ok(merged_results)
    }

    /// 获取相关上下文
    pub async fn get_context(&self, results: &[SearchResult]) -> Result<ContextInfo> {
        info!("合成上下文，基于 {} 个搜索结果", results.len());

        let context_info = self.context_synthesizer
            .synthesize_context(results)
            .await?;

        info!(
            "上下文合成完成，长度: {} tokens，置信度: {:.2}", 
            context_info.token_count,
            context_info.confidence
        );

        Ok(context_info)
    }

    /// 搜索并获取上下文（便捷方法）
    pub async fn search_with_context(&self, query: &str) -> Result<ContextInfo> {
        let results = self.search(query).await?;
        self.get_context(&results).await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> Result<KnowledgeStats> {
        let storage_stats = self.storage.get_stats().await?;
        
        Ok(KnowledgeStats {
            total_chunks: storage_stats.total_chunks,
            total_files: storage_stats.total_files,
            total_languages: storage_stats.total_languages,
            database_size: storage_stats.database_size,
            last_updated: storage_stats.last_updated,
        })
    }

    /// 清理知识库
    pub async fn clear(&mut self) -> Result<()> {
        info!("清理知识库...");
        self.storage.clear().await?;
        info!("知识库清理完成");
        Ok(())
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &Config {
        &self.config
    }

    /// 更新配置（需要重新初始化相关组件）
    pub async fn update_config(&mut self, new_config: Config) -> Result<()> {
        info!("更新知识库配置...");

        // 验证新配置
        new_config.validate()?;

        // 如果数据库配置发生变化，需要重新连接
        if self.config.database.path != new_config.database.path {
            warn!("数据库路径发生变化，需要重新初始化存储引擎");
            self.storage = VectorDatabase::new(&new_config.database).await?;
        }

        // 如果嵌入配置发生变化，需要重新初始化嵌入引擎
        if self.config.embedding.model_type != new_config.embedding.model_type ||
           self.config.embedding.model_path != new_config.embedding.model_path {
            warn!("嵌入模型配置发生变化，需要重新初始化嵌入引擎");
            self.embedding = EmbeddingEngine::new(&new_config.embedding).await?;
        }

        // 更新配置
        self.config = new_config;

        info!("配置更新完成");
        Ok(())
    }

    /// 优化知识库
    pub async fn optimize(&mut self) -> Result<()> {
        info!("优化知识库...");
        self.storage.optimize().await?;
        info!("知识库优化完成");
        Ok(())
    }
}

/// 知识库统计信息
#[derive(Debug, Clone)]
pub struct KnowledgeStats {
    /// 总代码块数
    pub total_chunks: usize,
    /// 总文件数
    pub total_files: usize,
    /// 总语言数
    pub total_languages: usize,
    /// 数据库大小（字节）
    pub database_size: u64,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;


    #[tokio::test]
    async fn test_knowledge_base_creation() {
        let config = Config::default();
        let kb = KnowledgeBase::new(config).await;
        assert!(kb.is_ok());
    }

    #[tokio::test]
    async fn test_search_empty_database() {
        let config = Config::default();
        let kb = KnowledgeBase::new(config).await.unwrap();
        
        let results = kb.search("test query").await.unwrap();
        assert!(results.is_empty());
    }
}