//! # 查询解析器
//!
//! 解析用户查询并提取搜索意图。

use crate::{
    error::Result,
    query::{ProcessedQuery, QueryType, SymbolType},
};
use regex::Regex;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// 查询过滤器
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct QueryFilters {
    /// 语言过滤
    pub language: Option<String>,
    /// 文件路径过滤
    pub file_path: Option<String>,
    /// 符号类型过滤
    pub symbol_type: Option<SymbolType>,
    /// 文件扩展名过滤
    pub file_extension: Option<String>,
    /// 作者过滤
    pub author: Option<String>,
    /// 日期范围过滤
    pub date_range: Option<DateRange>,
    /// 行数范围过滤
    pub line_range: Option<LineRange>,
    /// 复杂度过滤
    pub complexity: Option<ComplexityFilter>,
}

/// 日期范围
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DateRange {
    pub start: Option<chrono::DateTime<chrono::Utc>>,
    pub end: Option<chrono::DateTime<chrono::Utc>>,
}

/// 行数范围
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineRange {
    pub min: Option<usize>,
    pub max: Option<usize>,
}

/// 复杂度过滤
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComplexityFilter {
    Low,
    Medium,
    High,
    Range(u32, u32),
}

/// 查询操作符
#[derive(Debug, Clone, PartialEq)]
pub enum QueryOperator {
    And,
    Or,
    Not,
    Near(u32), // NEAR/n 操作符，指定词距
}

/// 查询项
#[derive(Debug, Clone)]
pub struct QueryTerm {
    pub text: String,
    pub is_phrase: bool,
    pub is_wildcard: bool,
    pub boost: f32,
    pub operator: Option<QueryOperator>,
}

/// 查询解析器
pub struct QueryParser {
    /// 预编译的正则表达式
    regexes: QueryRegexes,
    /// 停用词列表
    stop_words: std::collections::HashSet<String>,
    /// 编程语言关键词映射
    language_keywords: HashMap<String, Vec<String>>,
}

/// 预编译的正则表达式
struct QueryRegexes {
    /// 引号包围的短语
    phrase_regex: Regex,
    /// 过滤条件
    filter_regex: Regex,
    /// 通配符
    wildcard_regex: Regex,
    /// 布尔操作符
    boolean_regex: Regex,
    /// 提升权重
    boost_regex: Regex,
    /// NEAR 操作符
    near_regex: Regex,
}

impl QueryRegexes {
    fn new() -> Self {
        Self {
            phrase_regex: Regex::new(r#""([^"]+)""#).unwrap(),
            filter_regex: Regex::new(r"(\w+):(\S+)").unwrap(),
            wildcard_regex: Regex::new(r"\*|\?").unwrap(),
            boolean_regex: Regex::new(r"\b(AND|OR|NOT)\b").unwrap(),
            boost_regex: Regex::new(r"(\S+)\^(\d+(?:\.\d+)?)").unwrap(),
            near_regex: Regex::new(r"\bNEAR/(\d+)\b").unwrap(),
        }
    }
}

impl QueryParser {
    /// 创建新的查询解析器
    pub fn new() -> Self {
        Self {
            regexes: QueryRegexes::new(),
            stop_words: Self::load_stop_words(),
            language_keywords: Self::load_language_keywords(),
        }
    }

    /// 加载停用词
    fn load_stop_words() -> std::collections::HashSet<String> {
        let mut stop_words = std::collections::HashSet::new();

        // 英文停用词
        let english_stop_words = vec![
            "a", "an", "and", "are", "as", "at", "be", "by", "for", "from",
            "has", "he", "in", "is", "it", "its", "of", "on", "that", "the",
            "to", "was", "will", "with", "the", "this", "but", "they", "have",
            "had", "what", "said", "each", "which", "she", "do", "how", "their",
            "if", "up", "out", "many", "then", "them", "these", "so", "some",
        ];

        for word in english_stop_words {
            stop_words.insert(word.to_string());
        }

        // 编程相关停用词
        let programming_stop_words = vec![
            "get", "set", "new", "old", "add", "remove", "delete", "update",
            "create", "make", "build", "run", "start", "stop", "end", "begin",
        ];

        for word in programming_stop_words {
            stop_words.insert(word.to_string());
        }

        stop_words
    }

    /// 加载编程语言关键词
    fn load_language_keywords() -> HashMap<String, Vec<String>> {
        let mut keywords = HashMap::new();

        // Rust 关键词
        keywords.insert("rust".to_string(), vec![
            "fn", "struct", "enum", "impl", "trait", "mod", "use", "pub",
            "let", "mut", "const", "static", "match", "if", "else", "loop",
            "while", "for", "break", "continue", "return", "async", "await",
        ].into_iter().map(|s| s.to_string()).collect());

        // JavaScript 关键词
        keywords.insert("javascript".to_string(), vec![
            "function", "class", "const", "let", "var", "if", "else", "for",
            "while", "do", "switch", "case", "break", "continue", "return",
            "async", "await", "try", "catch", "finally", "throw", "new",
        ].into_iter().map(|s| s.to_string()).collect());

        // Python 关键词
        keywords.insert("python".to_string(), vec![
            "def", "class", "if", "elif", "else", "for", "while", "try",
            "except", "finally", "with", "as", "import", "from", "return",
            "yield", "lambda", "and", "or", "not", "in", "is", "async", "await",
        ].into_iter().map(|s| s.to_string()).collect());

        keywords
    }

    /// 解析查询
    pub fn parse(&self, query: &str) -> Result<ProcessedQuery> {
        let query = query.trim();

        // 提取过滤条件
        let filters = self.extract_filters(query);

        // 移除过滤条件后的查询文本
        let cleaned_query = self.remove_filters(query);

        // 检测查询类型
        let query_type = self.detect_query_type(&cleaned_query, &filters);

        // 解析查询项
        let query_terms = self.parse_query_terms(&cleaned_query)?;

        // 提取关键词
        let keywords = self.extract_keywords_from_terms(&query_terms);

        // 清理查询文本
        let cleaned_text = self.clean_query_text(&cleaned_query);

        let enable_hybrid = !matches!(query_type, QueryType::Exact);

        Ok(ProcessedQuery {
            original_text: query.to_string(),
            text: cleaned_text,
            keywords,
            query_type,
            top_k: self.extract_top_k(query).unwrap_or(10),
            enable_hybrid,
            language_filter: filters.language,
            file_filter: filters.file_path,
            symbol_filter: filters.symbol_type,
        })
    }

    /// 提取所有过滤条件
    fn extract_filters(&self, query: &str) -> QueryFilters {
        let mut filters = QueryFilters::default();

        // 使用正则表达式提取过滤条件
        for cap in self.regexes.filter_regex.captures_iter(query) {
            let key = cap.get(1).unwrap().as_str().to_lowercase();
            let value = cap.get(2).unwrap().as_str();

            match key.as_str() {
                "lang" | "language" => filters.language = Some(value.to_string()),
                "file" | "path" => filters.file_path = Some(value.to_string()),
                "ext" | "extension" => filters.file_extension = Some(value.to_string()),
                "type" | "symbol" => filters.symbol_type = self.parse_symbol_type(value),
                "author" => filters.author = Some(value.to_string()),
                "lines" => filters.line_range = self.parse_line_range(value),
                "complexity" => filters.complexity = self.parse_complexity_filter(value),
                _ => {} // 忽略未知过滤器
            }
        }

        filters
    }

    /// 移除过滤条件
    fn remove_filters(&self, query: &str) -> String {
        self.regexes.filter_regex.replace_all(query, "").trim().to_string()
    }

    /// 解析查询项
    fn parse_query_terms(&self, query: &str) -> Result<Vec<QueryTerm>> {
        let mut terms = Vec::new();
        let mut processed_ranges = Vec::new();

        // 首先提取短语查询
        for cap in self.regexes.phrase_regex.captures_iter(query) {
            let phrase = cap.get(1).unwrap().as_str();
            let full_match = cap.get(0).unwrap();

            terms.push(QueryTerm {
                text: phrase.to_string(),
                is_phrase: true,
                is_wildcard: false,
                boost: 1.0,
                operator: None,
            });

            processed_ranges.push((full_match.start(), full_match.end()));
        }

        // 提取带权重的词
        for cap in self.regexes.boost_regex.captures_iter(query) {
            let term = cap.get(1).unwrap().as_str();
            let boost: f32 = cap.get(2).unwrap().as_str().parse().unwrap_or(1.0);
            let full_match = cap.get(0).unwrap();

            // 检查是否与已处理的范围重叠
            if !self.overlaps_with_ranges(&processed_ranges, full_match.start(), full_match.end()) {
                terms.push(QueryTerm {
                    text: term.to_string(),
                    is_phrase: false,
                    is_wildcard: self.regexes.wildcard_regex.is_match(term),
                    boost,
                    operator: None,
                });

                processed_ranges.push((full_match.start(), full_match.end()));
            }
        }

        // 处理剩余的普通词汇
        let mut current_pos = 0;
        for word in query.split_whitespace() {
            let word_start = query[current_pos..].find(word).unwrap() + current_pos;
            let word_end = word_start + word.len();

            if !self.overlaps_with_ranges(&processed_ranges, word_start, word_end) {
                if word.len() > 1 && !self.stop_words.contains(&word.to_lowercase()) {
                    let operator = self.detect_operator(word);
                    let clean_word = self.clean_word(word);

                    if !clean_word.is_empty() {
                        terms.push(QueryTerm {
                            text: clean_word,
                            is_phrase: false,
                            is_wildcard: self.regexes.wildcard_regex.is_match(word),
                            boost: 1.0,
                            operator,
                        });
                    }
                }
            }

            current_pos = word_end;
        }

        Ok(terms)
    }

    /// 检查范围是否重叠
    fn overlaps_with_ranges(&self, ranges: &[(usize, usize)], start: usize, end: usize) -> bool {
        ranges.iter().any(|(r_start, r_end)| {
            !(end <= *r_start || start >= *r_end)
        })
    }

    /// 从查询项中提取关键词
    fn extract_keywords_from_terms(&self, terms: &[QueryTerm]) -> Vec<String> {
        terms.iter()
            .filter(|term| !term.is_phrase || term.text.len() > 2)
            .map(|term| term.text.to_lowercase())
            .collect()
    }

    /// 提取 top_k 参数
    fn extract_top_k(&self, query: &str) -> Option<usize> {
        if let Some(cap) = Regex::new(r"top:(\d+)").unwrap().captures(query) {
            cap.get(1).unwrap().as_str().parse().ok()
        } else {
            None
        }
    }

    /// 解析符号类型
    fn parse_symbol_type(&self, value: &str) -> Option<SymbolType> {
        match value.to_lowercase().as_str() {
            "function" | "func" | "fn" => Some(SymbolType::Function),
            "class" => Some(SymbolType::Class),
            "struct" => Some(SymbolType::Struct),
            "interface" => Some(SymbolType::Interface),
            "enum" => Some(SymbolType::Enum),
            "const" | "constant" => Some(SymbolType::Constant),
            "var" | "variable" => Some(SymbolType::Variable),
            _ => None,
        }
    }

    /// 解析行数范围
    fn parse_line_range(&self, value: &str) -> Option<LineRange> {
        if let Some(cap) = Regex::new(r"(\d+)-(\d+)").unwrap().captures(value) {
            let min: usize = cap.get(1).unwrap().as_str().parse().ok()?;
            let max: usize = cap.get(2).unwrap().as_str().parse().ok()?;
            Some(LineRange { min: Some(min), max: Some(max) })
        } else if let Ok(num) = value.parse::<usize>() {
            Some(LineRange { min: Some(num), max: Some(num) })
        } else {
            None
        }
    }

    /// 解析复杂度过滤器
    fn parse_complexity_filter(&self, value: &str) -> Option<ComplexityFilter> {
        match value.to_lowercase().as_str() {
            "low" => Some(ComplexityFilter::Low),
            "medium" | "med" => Some(ComplexityFilter::Medium),
            "high" => Some(ComplexityFilter::High),
            _ => {
                if let Some(cap) = Regex::new(r"(\d+)-(\d+)").unwrap().captures(value) {
                    let min: u32 = cap.get(1).unwrap().as_str().parse().ok()?;
                    let max: u32 = cap.get(2).unwrap().as_str().parse().ok()?;
                    Some(ComplexityFilter::Range(min, max))
                } else {
                    None
                }
            }
        }
    }

    /// 检测操作符
    fn detect_operator(&self, word: &str) -> Option<QueryOperator> {
        match word.to_uppercase().as_str() {
            "AND" => Some(QueryOperator::And),
            "OR" => Some(QueryOperator::Or),
            "NOT" => Some(QueryOperator::Not),
            _ => {
                if let Some(cap) = self.regexes.near_regex.captures(word) {
                    let distance: u32 = cap.get(1).unwrap().as_str().parse().unwrap_or(5);
                    Some(QueryOperator::Near(distance))
                } else {
                    None
                }
            }
        }
    }

    /// 清理单词
    fn clean_word(&self, word: &str) -> String {
        word.trim_matches(|c: char| !c.is_alphanumeric() && c != '*' && c != '?')
            .to_lowercase()
    }

    /// 检测查询类型（增强版）
    fn detect_query_type(&self, query: &str, filters: &QueryFilters) -> QueryType {
        let query_lower = query.to_lowercase();

        // 检查是否有明确的类型指示符
        if query_lower.contains("exact:") || self.regexes.phrase_regex.is_match(query) {
            return QueryType::Exact;
        }

        if query_lower.contains("code:") {
            return QueryType::Code;
        }

        if query_lower.contains("doc:") || query_lower.contains("comment:") {
            return QueryType::Documentation;
        }

        // 基于过滤器判断
        if filters.symbol_type.is_some() {
            return QueryType::Symbol;
        }

        // 基于查询内容智能判断
        let symbol_keywords: Vec<String> = vec![
            "function", "method", "func", "def", "class", "struct", "enum",
            "interface", "trait", "impl", "constructor", "destructor",
        ].into_iter().map(|s| s.to_string()).collect();

        let code_keywords: Vec<String> = vec![
            "algorithm", "implementation", "logic", "code", "snippet",
            "example", "pattern", "template", "framework",
        ].into_iter().map(|s| s.to_string()).collect();

        let doc_keywords: Vec<String> = vec![
            "documentation", "comment", "readme", "guide", "tutorial",
            "example", "usage", "api", "reference",
        ].into_iter().map(|s| s.to_string()).collect();

        // 计算各类型的匹配分数
        let symbol_score = self.calculate_keyword_score(&query_lower, &symbol_keywords);
        let code_score = self.calculate_keyword_score(&query_lower, &code_keywords);
        let doc_score = self.calculate_keyword_score(&query_lower, &doc_keywords);

        // 检查是否包含编程语言特定关键词
        if let Some(lang) = &filters.language {
            if let Some(lang_keywords) = self.language_keywords.get(lang) {
                let lang_score = self.calculate_keyword_score(&query_lower, lang_keywords);
                if lang_score > 0 {
                    return QueryType::Code;
                }
            }
        }

        // 根据最高分数确定类型
        if symbol_score > code_score && symbol_score > doc_score && symbol_score > 0 {
            QueryType::Symbol
        } else if code_score > doc_score && code_score > 0 {
            QueryType::Code
        } else if doc_score > 0 {
            QueryType::Documentation
        } else {
            // 默认使用混合搜索
            QueryType::Hybrid
        }
    }

    /// 计算关键词匹配分数
    fn calculate_keyword_score(&self, query: &str, keywords: &[String]) -> usize {
        keywords.iter()
            .filter(|keyword| query.contains(keyword.as_str()))
            .count()
    }





    /// 清理查询文本
    fn clean_query_text(&self, query: &str) -> String {
        // 使用正则表达式移除所有过滤条件
        let cleaned = self.regexes.filter_regex.replace_all(query, "");

        // 移除多余的空格并返回
        cleaned.split_whitespace()
            .collect::<Vec<_>>()
            .join(" ")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_type_detection() {
        let parser = QueryParser::new();
        
        let query = parser.parse("find function hello").unwrap();
        assert!(matches!(query.query_type, QueryType::Symbol));
        
        let query = parser.parse("exact: println!").unwrap();
        assert!(matches!(query.query_type, QueryType::Exact));
        
        let query = parser.parse("code: impl trait").unwrap();
        assert!(matches!(query.query_type, QueryType::Code));
    }

    #[test]
    fn test_filter_extraction() {
        let parser = QueryParser::new();
        
        let query = parser.parse("lang:rust find function").unwrap();
        assert_eq!(query.language_filter, Some("rust".to_string()));
        
        let query = parser.parse("file:main.rs hello world").unwrap();
        assert_eq!(query.file_filter, Some("main.rs".to_string()));
    }

    #[test]
    fn test_keyword_extraction() {
        let parser = QueryParser::new();
        
        let query = parser.parse("find hello world function").unwrap();
        assert!(query.keywords.contains(&"find".to_string()));
        assert!(query.keywords.contains(&"hello".to_string()));
        assert!(query.keywords.contains(&"world".to_string()));
        assert!(query.keywords.contains(&"function".to_string()));
    }
}