//! # 混合检索算法
//!
//! 结合向量搜索和关键词搜索，实现加权融合的混合检索算法。

use crate::{
    error::Result,
    embedding::Embedding,
    knowledge_base::SearchResult,
    storage::{
        vector_db::{VectorIndex, DistanceMetric},
        keyword_search::{KeywordSearchEngine, SearchAlgorithm},
    },
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use rusqlite::Connection;

/// 混合搜索配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HybridSearchConfig {
    /// 向量搜索权重 (0.0-1.0)
    pub vector_weight: f64,
    /// 关键词搜索权重 (0.0-1.0)
    pub keyword_weight: f64,
    /// 融合算法类型
    pub fusion_algorithm: FusionAlgorithm,
    /// 向量搜索的距离度量
    pub vector_metric: DistanceMetric,
    /// 关键词搜索算法
    pub keyword_algorithm: SearchAlgorithm,
    /// 最小相似度阈值
    pub min_similarity_threshold: f32,
    /// 结果重排序策略
    pub rerank_strategy: RerankStrategy,
}

impl Default for HybridSearchConfig {
    fn default() -> Self {
        Self {
            vector_weight: 0.7,
            keyword_weight: 0.3,
            fusion_algorithm: FusionAlgorithm::WeightedSum,
            vector_metric: DistanceMetric::Cosine,
            keyword_algorithm: SearchAlgorithm::BM25,
            min_similarity_threshold: 0.1,
            rerank_strategy: RerankStrategy::ScoreBased,
        }
    }
}

/// 融合算法类型
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum FusionAlgorithm {
    /// 加权求和
    WeightedSum,
    /// 倒数排名融合 (Reciprocal Rank Fusion)
    ReciprocalRankFusion,
    /// 最大值融合
    MaxScore,
    /// 最小值融合
    MinScore,
    /// 调和平均
    HarmonicMean,
}

/// 重排序策略
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum RerankStrategy {
    /// 基于分数排序
    ScoreBased,
    /// 基于多样性排序
    DiversityBased,
    /// 基于相关性排序
    RelevanceBased,
}

/// 混合搜索引擎
pub struct HybridSearchEngine {
    vector_index: VectorIndex,
    keyword_engine: KeywordSearchEngine,
    config: HybridSearchConfig,
}

impl HybridSearchEngine {
    /// 创建新的混合搜索引擎
    pub async fn new(
        connection: Arc<RwLock<Connection>>,
        config: HybridSearchConfig,
    ) -> Result<Self> {
        let vector_index = VectorIndex::new(connection.clone()).await?;
        let keyword_engine = KeywordSearchEngine::new(connection).await?;

        Ok(Self {
            vector_index,
            keyword_engine,
            config,
        })
    }

    /// 混合搜索
    pub async fn search(
        &self,
        query_embedding: &Embedding,
        query_keywords: &[String],
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        // 执行向量搜索
        let vector_results = self.vector_index
            .search_similar_with_metric(
                query_embedding,
                top_k * 2, // 获取更多候选结果
                self.config.min_similarity_threshold,
                self.config.vector_metric,
            )
            .await?;

        // 执行关键词搜索
        let keyword_results = self.keyword_engine
            .search_keywords(query_keywords, top_k * 2)
            .await?;

        // 融合结果
        let fused_results = self.fuse_results(vector_results, keyword_results)?;

        // 重排序
        let reranked_results = self.rerank_results(fused_results)?;

        // 返回前 top_k 个结果
        Ok(reranked_results.into_iter().take(top_k).collect())
    }

    /// 融合搜索结果
    fn fuse_results(
        &self,
        vector_results: Vec<SearchResult>,
        keyword_results: Vec<SearchResult>,
    ) -> Result<Vec<SearchResult>> {
        let mut chunk_scores: HashMap<String, (SearchResult, f64)> = HashMap::new();

        // 处理向量搜索结果
        for (rank, result) in vector_results.into_iter().enumerate() {
            let vector_score = match self.config.fusion_algorithm {
                FusionAlgorithm::ReciprocalRankFusion => 1.0 / (rank as f64 + 60.0), // RRF with k=60
                _ => result.score as f64,
            };

            chunk_scores.insert(
                result.chunk_id.clone(),
                (result, vector_score * self.config.vector_weight),
            );
        }

        // 处理关键词搜索结果
        for (rank, result) in keyword_results.into_iter().enumerate() {
            let keyword_score = match self.config.fusion_algorithm {
                FusionAlgorithm::ReciprocalRankFusion => 1.0 / (rank as f64 + 60.0),
                _ => result.score as f64,
            };

            if let Some((existing_result, existing_score)) = chunk_scores.get_mut(&result.chunk_id) {
                // 融合分数
                let fused_score = self.calculate_fused_score(
                    *existing_score,
                    keyword_score * self.config.keyword_weight,
                );
                *existing_score = fused_score;
            } else {
                chunk_scores.insert(
                    result.chunk_id.clone(),
                    (result, keyword_score * self.config.keyword_weight),
                );
            }
        }

        // 转换为结果列表
        let mut results: Vec<SearchResult> = chunk_scores
            .into_iter()
            .map(|(_, (mut result, score))| {
                result.score = score as f32;
                result
            })
            .collect();

        // 按分数排序
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(results)
    }

    /// 计算融合分数
    fn calculate_fused_score(&self, vector_score: f64, keyword_score: f64) -> f64 {
        match self.config.fusion_algorithm {
            FusionAlgorithm::WeightedSum => vector_score + keyword_score,
            FusionAlgorithm::ReciprocalRankFusion => vector_score + keyword_score,
            FusionAlgorithm::MaxScore => vector_score.max(keyword_score),
            FusionAlgorithm::MinScore => vector_score.min(keyword_score),
            FusionAlgorithm::HarmonicMean => {
                if vector_score + keyword_score == 0.0 {
                    0.0
                } else {
                    2.0 * vector_score * keyword_score / (vector_score + keyword_score)
                }
            }
        }
    }

    /// 重排序结果
    fn rerank_results(&self, mut results: Vec<SearchResult>) -> Result<Vec<SearchResult>> {
        match self.config.rerank_strategy {
            RerankStrategy::ScoreBased => {
                // 已经按分数排序了
                Ok(results)
            }
            RerankStrategy::DiversityBased => {
                // 基于多样性的重排序
                self.diversity_rerank(results)
            }
            RerankStrategy::RelevanceBased => {
                // 基于相关性的重排序
                self.relevance_rerank(results)
            }
        }
    }

    /// 多样性重排序
    fn diversity_rerank(&self, mut results: Vec<SearchResult>) -> Result<Vec<SearchResult>> {
        if results.len() <= 1 {
            return Ok(results);
        }

        let mut reranked = Vec::new();
        let mut remaining = results.clone();
        let total_len = results.len();

        // 选择第一个最高分的结果
        if !remaining.is_empty() {
            reranked.push(remaining.remove(0));
        }

        // 基于多样性选择后续结果
        while !remaining.is_empty() && reranked.len() < total_len {
            let mut best_idx = 0;
            let mut best_diversity_score = 0.0;

            for (idx, candidate) in remaining.iter().enumerate() {
                // 计算与已选结果的多样性
                let mut diversity_score = candidate.score as f64;
                
                for selected in &reranked {
                    // 基于文件路径和语言的多样性
                    if candidate.file_path != selected.file_path {
                        diversity_score += 0.1;
                    }
                    if candidate.language != selected.language {
                        diversity_score += 0.05;
                    }
                }

                if diversity_score > best_diversity_score {
                    best_diversity_score = diversity_score;
                    best_idx = idx;
                }
            }

            reranked.push(remaining.remove(best_idx));
        }

        Ok(reranked)
    }

    /// 相关性重排序
    fn relevance_rerank(&self, results: Vec<SearchResult>) -> Result<Vec<SearchResult>> {
        // 简单的相关性重排序：优先考虑符号名称匹配
        let mut reranked = results;
        
        reranked.sort_by(|a, b| {
            // 首先按原始分数排序
            let score_cmp = b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal);
            
            if score_cmp == std::cmp::Ordering::Equal {
                // 如果分数相同，优先考虑有符号名称的结果
                match (&a.symbol_name, &b.symbol_name) {
                    (Some(_), None) => std::cmp::Ordering::Less,
                    (None, Some(_)) => std::cmp::Ordering::Greater,
                    _ => std::cmp::Ordering::Equal,
                }
            } else {
                score_cmp
            }
        });

        Ok(reranked)
    }

    /// 更新配置
    pub fn update_config(&mut self, config: HybridSearchConfig) {
        self.config = config;
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &HybridSearchConfig {
        &self.config
    }
}
