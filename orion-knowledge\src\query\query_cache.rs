//! # 查询缓存机制
//!
//! 提供智能查询缓存功能，支持 LRU 淘汰、缓存失效、缓存预热等功能。

use crate::{
    error::Result,
    knowledge_base::SearchResult,
    query::ProcessedQuery,
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::hash::{<PERSON>h, <PERSON><PERSON>};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::Mutex;

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// 最大缓存条目数
    pub max_entries: usize,
    /// 缓存过期时间（秒）
    pub ttl_seconds: u64,
    /// 是否启用缓存
    pub enabled: bool,
    /// 缓存命中率统计间隔
    pub stats_interval_seconds: u64,
    /// 预热缓存大小
    pub preload_size: usize,
    /// 是否启用压缩
    pub enable_compression: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 1000,
            ttl_seconds: 3600, // 1小时
            enabled: true,
            stats_interval_seconds: 300, // 5分钟
            preload_size: 100,
            enable_compression: false,
        }
    }
}

/// 缓存条目
#[derive(Debug, Clone)]
struct CacheEntry {
    /// 查询结果
    results: Vec<SearchResult>,
    /// 创建时间
    created_at: Instant,
    /// 最后访问时间
    last_accessed: Instant,
    /// 访问次数
    access_count: u64,
    /// 查询耗时（毫秒）
    query_duration_ms: u64,
}

impl CacheEntry {
    fn new(results: Vec<SearchResult>, query_duration_ms: u64) -> Self {
        let now = Instant::now();
        Self {
            results,
            created_at: now,
            last_accessed: now,
            access_count: 1,
            query_duration_ms,
        }
    }

    fn is_expired(&self, ttl: Duration) -> bool {
        self.created_at.elapsed() > ttl
    }

    fn touch(&mut self) {
        self.last_accessed = Instant::now();
        self.access_count += 1;
    }
}

/// 缓存键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct CacheKey {
    /// 查询文本
    query_text: String,
    /// 查询类型
    query_type: String,
    /// 关键词（排序后）
    keywords: Vec<String>,
    /// 过滤条件
    filters: String,
    /// top_k 参数
    top_k: usize,
}

impl CacheKey {
    fn from_query(query: &ProcessedQuery) -> Self {
        let mut keywords = query.keywords.clone();
        keywords.sort();
        
        let filters = format!(
            "lang:{:?},file:{:?},symbol:{:?}",
            query.language_filter,
            query.file_filter,
            query.symbol_filter
        );

        Self {
            query_text: query.text.clone(),
            query_type: format!("{:?}", query.query_type),
            keywords,
            filters,
            top_k: query.top_k,
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct CacheStats {
    /// 总查询次数
    pub total_queries: u64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 缓存命中率
    pub hit_rate: f64,
    /// 平均查询时间（毫秒）
    pub avg_query_time_ms: f64,
    /// 缓存节省的时间（毫秒）
    pub time_saved_ms: u64,
    /// 当前缓存条目数
    pub current_entries: usize,
    /// 最大缓存条目数
    pub max_entries: usize,
}

impl CacheStats {
    fn update_hit_rate(&mut self) {
        if self.total_queries > 0 {
            self.hit_rate = self.cache_hits as f64 / self.total_queries as f64;
        }
    }
}

/// 查询缓存
pub struct QueryCache {
    /// 缓存配置
    config: CacheConfig,
    /// 缓存存储
    cache: Arc<RwLock<HashMap<CacheKey, CacheEntry>>>,
    /// LRU 访问顺序
    lru_order: Arc<Mutex<VecDeque<CacheKey>>>,
    /// 缓存统计
    stats: Arc<RwLock<CacheStats>>,
    /// TTL 持续时间
    ttl: Duration,
}

impl QueryCache {
    /// 创建新的查询缓存
    pub fn new(config: CacheConfig) -> Self {
        let ttl = Duration::from_secs(config.ttl_seconds);
        
        Self {
            config: config.clone(),
            cache: Arc::new(RwLock::new(HashMap::new())),
            lru_order: Arc::new(Mutex::new(VecDeque::new())),
            stats: Arc::new(RwLock::new(CacheStats {
                max_entries: config.max_entries,
                ..Default::default()
            })),
            ttl,
        }
    }

    /// 获取缓存结果
    pub async fn get(&self, query: &ProcessedQuery) -> Option<Vec<SearchResult>> {
        if !self.config.enabled {
            return None;
        }

        let key = CacheKey::from_query(query);
        
        // 更新统计
        {
            let mut stats = self.stats.write().unwrap();
            stats.total_queries += 1;
        }

        // 检查缓存
        let mut cache = self.cache.write().unwrap();
        if let Some(entry) = cache.get_mut(&key) {
            // 检查是否过期
            if entry.is_expired(self.ttl) {
                cache.remove(&key);
                self.remove_from_lru(&key).await;
                
                // 更新统计
                let mut stats = self.stats.write().unwrap();
                stats.cache_misses += 1;
                stats.update_hit_rate();
                
                return None;
            }

            // 更新访问信息
            entry.touch();
            
            // 更新 LRU 顺序
            self.move_to_front(&key).await;
            
            // 更新统计
            {
                let mut stats = self.stats.write().unwrap();
                stats.cache_hits += 1;
                stats.time_saved_ms += entry.query_duration_ms;
                stats.update_hit_rate();
            }

            return Some(entry.results.clone());
        }

        // 缓存未命中
        {
            let mut stats = self.stats.write().unwrap();
            stats.cache_misses += 1;
            stats.update_hit_rate();
        }

        None
    }

    /// 存储查询结果到缓存
    pub async fn put(
        &self,
        query: &ProcessedQuery,
        results: Vec<SearchResult>,
        query_duration_ms: u64,
    ) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let key = CacheKey::from_query(query);
        let entry = CacheEntry::new(results, query_duration_ms);

        // 检查是否需要淘汰旧条目
        self.evict_if_needed().await?;

        // 存储到缓存
        {
            let mut cache = self.cache.write().unwrap();
            cache.insert(key.clone(), entry);
        }

        // 更新 LRU 顺序
        self.add_to_front(&key).await;

        // 更新统计
        {
            let mut stats = self.stats.write().unwrap();
            stats.current_entries = self.cache.read().unwrap().len();
        }

        Ok(())
    }

    /// 清空缓存
    pub async fn clear(&self) -> Result<()> {
        {
            let mut cache = self.cache.write().unwrap();
            cache.clear();
        }

        {
            let mut lru_order = self.lru_order.lock().await;
            lru_order.clear();
        }

        // 重置统计
        {
            let mut stats = self.stats.write().unwrap();
            *stats = CacheStats {
                max_entries: self.config.max_entries,
                ..Default::default()
            };
        }

        Ok(())
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        self.stats.read().unwrap().clone()
    }

    /// 缓存预热
    pub async fn preload(&self, common_queries: Vec<ProcessedQuery>) -> Result<()> {
        tracing::info!("开始缓存预热，预热查询数量: {}", common_queries.len());
        
        for query in common_queries.into_iter().take(self.config.preload_size) {
            // 这里应该执行实际的查询来填充缓存
            // 由于我们没有查询执行器的引用，这里只是占位
            tracing::debug!("预热查询: {}", query.text);
        }
        
        Ok(())
    }

    /// 淘汰过期和最少使用的条目
    async fn evict_if_needed(&self) -> Result<()> {
        let current_size = self.cache.read().unwrap().len();
        
        if current_size >= self.config.max_entries {
            // 首先移除过期条目
            self.remove_expired_entries().await?;
            
            // 如果还是超过限制，使用 LRU 淘汰
            let current_size = self.cache.read().unwrap().len();
            if current_size >= self.config.max_entries {
                self.evict_lru_entries(current_size - self.config.max_entries + 1).await?;
            }
        }
        
        Ok(())
    }

    /// 移除过期条目
    async fn remove_expired_entries(&self) -> Result<()> {
        let mut expired_keys = Vec::new();
        
        {
            let cache = self.cache.read().unwrap();
            for (key, entry) in cache.iter() {
                if entry.is_expired(self.ttl) {
                    expired_keys.push(key.clone());
                }
            }
        }

        for key in expired_keys {
            let mut cache = self.cache.write().unwrap();
            cache.remove(&key);
            drop(cache);
            
            self.remove_from_lru(&key).await;
        }

        Ok(())
    }

    /// 使用 LRU 策略淘汰条目
    async fn evict_lru_entries(&self, count: usize) -> Result<()> {
        let mut lru_order = self.lru_order.lock().await;
        let mut cache = self.cache.write().unwrap();
        
        for _ in 0..count {
            if let Some(key) = lru_order.pop_back() {
                cache.remove(&key);
            } else {
                break;
            }
        }
        
        Ok(())
    }

    /// 将键移动到 LRU 队列前端
    async fn move_to_front(&self, key: &CacheKey) {
        let mut lru_order = self.lru_order.lock().await;
        
        // 移除旧位置
        lru_order.retain(|k| k != key);
        
        // 添加到前端
        lru_order.push_front(key.clone());
    }

    /// 添加键到 LRU 队列前端
    async fn add_to_front(&self, key: &CacheKey) {
        let mut lru_order = self.lru_order.lock().await;
        lru_order.push_front(key.clone());
    }

    /// 从 LRU 队列中移除键
    async fn remove_from_lru(&self, key: &CacheKey) {
        let mut lru_order = self.lru_order.lock().await;
        lru_order.retain(|k| k != key);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::query::{QueryType, ProcessedQuery};

    fn create_test_query(text: &str) -> ProcessedQuery {
        ProcessedQuery {
            original_text: text.to_string(),
            text: text.to_string(),
            keywords: vec!["test".to_string()],
            query_type: QueryType::Semantic,
            top_k: 10,
            enable_hybrid: true,
            language_filter: None,
            file_filter: None,
            symbol_filter: None,
        }
    }

    fn create_test_results() -> Vec<SearchResult> {
        vec![
            SearchResult {
                chunk_id: "1".to_string(),
                file_path: "test.rs".to_string(),
                content: "test content".to_string(),
                score: 0.9,
                language: "rust".to_string(),
                symbol_name: Some("test_fn".to_string()),
                start_line: 1,
                end_line: 5,
            }
        ]
    }

    #[tokio::test]
    async fn test_cache_put_and_get() {
        let config = CacheConfig::default();
        let cache = QueryCache::new(config);

        let query = create_test_query("test query");
        let results = create_test_results();

        // 缓存应该为空
        assert!(cache.get(&query).await.is_none());

        // 存储结果
        cache.put(&query, results.clone(), 100).await.unwrap();

        // 应该能够获取结果
        let cached_results = cache.get(&query).await.unwrap();
        assert_eq!(cached_results.len(), results.len());
        assert_eq!(cached_results[0].chunk_id, results[0].chunk_id);
    }

    #[tokio::test]
    async fn test_cache_stats() {
        let config = CacheConfig::default();
        let cache = QueryCache::new(config);

        let query = create_test_query("test query");
        let results = create_test_results();

        // 初始统计
        let stats = cache.get_stats();
        assert_eq!(stats.total_queries, 0);
        assert_eq!(stats.cache_hits, 0);

        // 缓存未命中
        cache.get(&query).await;
        let stats = cache.get_stats();
        assert_eq!(stats.total_queries, 1);
        assert_eq!(stats.cache_misses, 1);

        // 存储并命中
        cache.put(&query, results, 100).await.unwrap();
        cache.get(&query).await;
        let stats = cache.get_stats();
        assert_eq!(stats.total_queries, 2);
        assert_eq!(stats.cache_hits, 1);
    }

    #[tokio::test]
    async fn test_cache_clear() {
        let config = CacheConfig::default();
        let cache = QueryCache::new(config);

        let query = create_test_query("test query");
        let results = create_test_results();

        // 存储结果
        cache.put(&query, results, 100).await.unwrap();
        assert!(cache.get(&query).await.is_some());

        // 清空缓存
        cache.clear().await.unwrap();
        assert!(cache.get(&query).await.is_none());

        // 统计应该重置
        let stats = cache.get_stats();
        assert_eq!(stats.current_entries, 0);
    }
}
